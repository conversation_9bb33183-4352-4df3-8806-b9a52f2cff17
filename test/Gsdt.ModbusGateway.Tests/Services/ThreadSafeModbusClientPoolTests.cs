using System.Collections.Concurrent;
using System.Diagnostics;
using FluentAssertions;
using Gsdt.ModbusGateway.Services;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Gsdt.ModbusGateway.Tests.Services;

/// <summary>
/// ThreadSafeModbusClientPool 测试�?/// 测试连接池的线程安全性和资源管理
/// </summary>
public class ThreadSafeModbusClientPoolTests
{
    private readonly Mock<ILogger<ThreadSafeModbusClientPool>> _loggerMock;
    private readonly Mock<ModbusPerformanceMonitor> _performanceMonitorMock;

    public ThreadSafeModbusClientPoolTests()
    {
        _loggerMock = new Mock<ILogger<ThreadSafeModbusClientPool>>();
        _performanceMonitorMock = new Mock<ModbusPerformanceMonitor>(Mock.Of<ILogger<ModbusPerformanceMonitor>>());
    }

    /// <summary>
    /// 测试基本的客户端注册和获取功�?    /// </summary>
    [Fact]
    public void RegisterClient_ShouldRegisterClientCorrectly()
    {
        // 准备
        var pool = new ThreadSafeModbusClientPool(_loggerMock.Object, _performanceMonitorMock.Object);
        var mockClient = new MockModbusClient();
        const int deviceId = 1;

        // 执行
        pool.RegisterClient(deviceId, mockClient);

        // 验证
        pool.IsDeviceRegistered(deviceId).Should().BeTrue();
        var wrapper = pool.GetClientWrapper(deviceId);
        wrapper.Should().NotBeNull();
        var rawClient = pool.GetRawClient(deviceId);
        rawClient.Should().Be(mockClient);
    }

    /// <summary>
    /// 测试多个设备的注�?    /// </summary>
    [Fact]
    public void RegisterMultipleClients_ShouldRegisterAllCorrectly()
    {
        // 准备
        var pool = new ThreadSafeModbusClientPool(_loggerMock.Object, _performanceMonitorMock.Object);
        var deviceCount = 10;
        var clients = new Dictionary<int, MockModbusClient>();

        // 执行
        for (int i = 1; i <= deviceCount; i++)
        {
            var client = new MockModbusClient();
            clients[i] = client;
            pool.RegisterClient(i, client);
        }

        // 验证
        pool.GetRegisteredDeviceIds().Should().HaveCount(deviceCount);
        
        for (int i = 1; i <= deviceCount; i++)
        {
            pool.IsDeviceRegistered(i).Should().BeTrue();
            pool.GetRawClient(i).Should().Be(clients[i]);
            pool.GetClientWrapper(i).Should().NotBeNull();
        }
    }

    /// <summary>
    /// 测试客户端移除功�?    /// </summary>
    [Fact]
    public void RemoveClient_ShouldRemoveClientCorrectly()
    {
        // 准备
        var pool = new ThreadSafeModbusClientPool(_loggerMock.Object, _performanceMonitorMock.Object);
        var mockClient = new MockModbusClient();
        const int deviceId = 1;

        pool.RegisterClient(deviceId, mockClient);
        pool.IsDeviceRegistered(deviceId).Should().BeTrue();

        // 执行
        pool.RemoveClient(deviceId);

        // 验证
        pool.IsDeviceRegistered(deviceId).Should().BeFalse();
        pool.GetClientWrapper(deviceId).Should().BeNull();
        pool.GetRawClient(deviceId).Should().BeNull();
    }

    /// <summary>
    /// 测试并发注册客户端的线程安全�?    /// </summary>
    [Fact]
    public async Task RegisterClient_ConcurrentRegistration_ShouldBeThreadSafe()
    {
        // 准备
        var pool = new ThreadSafeModbusClientPool(_loggerMock.Object, _performanceMonitorMock.Object);
        const int concurrentRegistrations = 50;
        var exceptions = new ConcurrentBag<Exception>();
        var registeredDevices = new ConcurrentBag<int>();

        // 执行
        var tasks = Enumerable.Range(1, concurrentRegistrations).Select(deviceId =>
            Task.Run(() =>
            {
                try
                {
                    var client = new MockModbusClient();
                    pool.RegisterClient(deviceId, client);
                    registeredDevices.Add(deviceId);
                }
                catch (Exception ex)
                {
                    exceptions.Add(ex);
                }
            })).ToArray();

        await Task.WhenAll(tasks);

        // 验证
        exceptions.Should().BeEmpty("并发注册不应该产生异�?);
        registeredDevices.Should().HaveCount(concurrentRegistrations);
        pool.GetRegisteredDeviceIds().Should().HaveCount(concurrentRegistrations);

        // 验证所有设备都正确注册
        for (int i = 1; i <= concurrentRegistrations; i++)
        {
            pool.IsDeviceRegistered(i).Should().BeTrue();
            pool.GetClientWrapper(i).Should().NotBeNull();
            pool.GetRawClient(i).Should().NotBeNull();
        }
    }

    /// <summary>
    /// 测试并发访问客户端包装器的线程安全�?    /// </summary>
    [Fact]
    public async Task GetClientWrapper_ConcurrentAccess_ShouldBeThreadSafe()
    {
        // 准备
        var pool = new ThreadSafeModbusClientPool(_loggerMock.Object, _performanceMonitorMock.Object);
        const int deviceCount = 10;
        const int accessesPerDevice = 20;

        // 注册设备
        for (int i = 1; i <= deviceCount; i++)
        {
            var client = new MockModbusClient();
            client.SetReadCoilsResponse(new byte[] { 0x55 });
            pool.RegisterClient(i, client);
        }

        var exceptions = new ConcurrentBag<Exception>();
        var successfulAccesses = new ConcurrentBag<int>();

        // 执行
        var tasks = Enumerable.Range(1, deviceCount).SelectMany(deviceId =>
            Enumerable.Range(0, accessesPerDevice).Select(_ =>
                Task.Run(async () =>
                {
                    try
                    {
                        var wrapper = pool.GetClientWrapper(deviceId);
                        wrapper.Should().NotBeNull();
                        
                        // 执行一个简单的操作来测试包装器
                        await wrapper!.ReadCoilsAsync(1, 0, 8, CancellationToken.None);
                        successfulAccesses.Add(deviceId);
                    }
                    catch (Exception ex)
                    {
                        exceptions.Add(ex);
                    }
                }))).ToArray();

        await Task.WhenAll(tasks);

        // 验证
        exceptions.Should().BeEmpty("并发访问不应该产生异�?);
        successfulAccesses.Should().HaveCount(deviceCount * accessesPerDevice);
    }

    /// <summary>
    /// 测试并发注册和移除的线程安全�?    /// </summary>
    [Fact]
    public async Task RegisterAndRemove_ConcurrentOperations_ShouldBeThreadSafe()
    {
        // 准备
        var pool = new ThreadSafeModbusClientPool(_loggerMock.Object, _performanceMonitorMock.Object);
        const int operationCount = 30;
        var exceptions = new ConcurrentBag<Exception>();
        var operations = new ConcurrentBag<string>();

        // 执行
        var tasks = Enumerable.Range(1, operationCount).Select(i =>
            Task.Run(() =>
            {
                try
                {
                    var deviceId = i % 10 + 1; // 使用1-10的设备ID
                    
                    if (i % 2 == 0)
                    {
                        // 注册操作
                        var client = new MockModbusClient();
                        pool.RegisterClient(deviceId, client);
                        operations.Add($"Register_{deviceId}");
                    }
                    else
                    {
                        // 移除操作
                        pool.RemoveClient(deviceId);
                        operations.Add($"Remove_{deviceId}");
                    }
                }
                catch (Exception ex)
                {
                    exceptions.Add(ex);
                }
            })).ToArray();

        await Task.WhenAll(tasks);

        // 验证
        exceptions.Should().BeEmpty("并发注册和移除不应该产生异常");
        operations.Should().HaveCount(operationCount);
    }

    /// <summary>
    /// 测试资源释放的线程安全�?    /// </summary>
    [Fact]
    public async Task Dispose_WithConcurrentOperations_ShouldBeThreadSafe()
    {
        // 准备
        var pool = new ThreadSafeModbusClientPool(_loggerMock.Object, _performanceMonitorMock.Object);
        const int deviceCount = 5;
        
        // 注册一些设�?        for (int i = 1; i <= deviceCount; i++)
        {
            var client = new MockModbusClient();
            client.SetReadCoilsResponse(new byte[] { 0x55 });
            client.SetOperationDelay(TimeSpan.FromMilliseconds(50));
            pool.RegisterClient(i, client);
        }

        // 启动一些长时间运行的操�?        var longRunningTasks = Enumerable.Range(1, deviceCount).Select(deviceId =>
            Task.Run(async () =>
            {
                try
                {
                    var wrapper = pool.GetClientWrapper(deviceId);
                    if (wrapper != null)
                    {
                        await wrapper.ReadCoilsAsync(1, 0, 8, CancellationToken.None);
                    }
                }
                catch (ObjectDisposedException)
                {
                    // 预期的异�?                }
                catch (Exception)
                {
                    // 其他异常也是可以接受�?                }
            })).ToArray();

        // 等待操作开�?        await Task.Delay(10);

        // 执行 - 释放连接�?        pool.Dispose();

        // 等待所有任务完�?        await Task.WhenAll(longRunningTasks);

        // 验证 - 释放后应该无法获取客户端
        for (int i = 1; i <= deviceCount; i++)
        {
            pool.GetClientWrapper(i).Should().BeNull();
            pool.GetRawClient(i).Should().BeNull();
        }
    }

    /// <summary>
    /// 测试性能监控集成
    /// </summary>
    [Fact]
    public async Task PerformanceMonitoring_Integration_ShouldWorkCorrectly()
    {
        // 准备
        var performanceLoggerMock = new Mock<ILogger<ModbusPerformanceMonitor>>(); var performanceMonitor = new ModbusPerformanceMonitor(performanceLoggerMock.Object);
        var pool = new ThreadSafeModbusClientPool(_loggerMock.Object, performanceMonitor);
        
        const int deviceId = 1;
        var mockClient = new MockModbusClient();
        mockClient.SetReadCoilsResponse(new byte[] { 0x55 });
        
        pool.RegisterClient(deviceId, mockClient);

        // 执行
        var wrapper = pool.GetClientWrapper(deviceId);
        wrapper.Should().NotBeNull();

        const int operationCount = 10;
        for (int i = 0; i < operationCount; i++)
        {
            await wrapper!.ReadCoilsAsync(1, 0, 8, CancellationToken.None);
        }

        // 验证性能监控
        var metrics = performanceMonitor.GetDeviceMetrics(deviceId);
        metrics.Should().NotBeNull();
        metrics!.TotalRequests.Should().Be(operationCount);
        metrics.SuccessfulRequests.Should().Be(operationCount);
        metrics.FailedRequests.Should().Be(0);
    }

    /// <summary>
    /// 压力测试：大量设备和操作
    /// </summary>
    [Fact]
    public async Task StressTest_ManyDevicesAndOperations_ShouldMaintainPerformance()
    {
        // 准备
        var pool = new ThreadSafeModbusClientPool(_loggerMock.Object, _performanceMonitorMock.Object);
        const int deviceCount = 20;
        const int operationsPerDevice = 25;
        
        // 注册设备
        for (int i = 1; i <= deviceCount; i++)
        {
            var client = new MockModbusClient();
            client.SetReadCoilsResponse(new byte[] { 0x55 });
            client.SetOperationDelay(TimeSpan.FromMilliseconds(1));
            pool.RegisterClient(i, client);
        }

        var exceptions = new ConcurrentBag<Exception>();
        var completedOperations = new ConcurrentBag<int>();
        var stopwatch = Stopwatch.StartNew();

        // 执行
        var tasks = Enumerable.Range(1, deviceCount).SelectMany(deviceId =>
            Enumerable.Range(0, operationsPerDevice).Select(opIndex =>
                Task.Run(async () =>
                {
                    try
                    {
                        var wrapper = pool.GetClientWrapper(deviceId);
                        wrapper.Should().NotBeNull();
                        
                        await wrapper!.ReadCoilsAsync(1, (ushort)opIndex, 8, CancellationToken.None);
                        completedOperations.Add(deviceId * 1000 + opIndex);
                    }
                    catch (Exception ex)
                    {
                        exceptions.Add(ex);
                    }
                }))).ToArray();

        await Task.WhenAll(tasks);
        stopwatch.Stop();

        // 验证
        var totalOperations = deviceCount * operationsPerDevice;
        exceptions.Should().BeEmpty("压力测试中不应该有异�?);
        completedOperations.Should().HaveCount(totalOperations);
        
        // 性能验证
        var averageTimePerOperation = stopwatch.ElapsedMilliseconds / (double)totalOperations;
        averageTimePerOperation.Should().BeLessThan(50, "平均每个操作的时间应该在合理范围�?);
        
        // 验证所有设备仍然可�?        for (int i = 1; i <= deviceCount; i++)
        {
            pool.IsDeviceRegistered(i).Should().BeTrue();
            pool.GetClientWrapper(i).Should().NotBeNull();
        }
    }
}
