using System.Collections.Concurrent;
using System.Diagnostics;
using FluentAssertions;
using Gsdt.ModbusGateway.Models;
using Gsdt.ModbusGateway.Services;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Gsdt.ModbusGateway.Tests.Services;

/// <summary>
/// ModbusClientWrapper 线程安全性测试类
/// 测试在高并发场景下的线程安全性和性能表现
/// </summary>
public class ModbusClientWrapperTests
{
    private readonly Mock<ILogger> _loggerMock;
    private readonly Mock<ModbusPerformanceMonitor> _performanceMonitorMock;
    private const int DeviceId = 1;

    public ModbusClientWrapperTests()
    {
        _loggerMock = new Mock<ILogger>();
        _performanceMonitorMock = new Mock<ModbusPerformanceMonitor>(Mock.Of<ILogger<ModbusPerformanceMonitor>>());
    }

    /// <summary>
    /// 测试单线程操作的基本功能
    /// </summary>
    [Fact]
    public async Task ReadCoilsAsync_SingleThread_ShouldWorkCorrectly()
    {
        // 准备
        var mockClient = new MockModbusClient();
        var wrapper = new ModbusClientWrapper(mockClient, _loggerMock.Object, DeviceId, _performanceMonitorMock.Object);

        byte slaveId = 1;
        ushort startAddress = 0;
        ushort quantity = 10;
        var expectedData = new byte[] { 0x55, 0x01 };
        mockClient.SetReadCoilsResponse(expectedData);

        // 执行
        var result = await wrapper.ReadCoilsAsync(slaveId, startAddress, quantity, CancellationToken.None);

        // 验证
        result.ToArray().Should().BeEquivalentTo(expectedData);
        mockClient.ReadCoilsCalled.Should().BeTrue();
        mockClient.LastSlaveId.Should().Be(slaveId);
        mockClient.LastStartAddress.Should().Be(startAddress);
        mockClient.LastQuantity.Should().Be(quantity);
    }

    /// <summary>
    /// 测试多线程并发读取操作的线程安全�?    /// </summary>
    [Fact]
    public async Task ReadCoilsAsync_MultipleThreads_ShouldBeThreadSafe()
    {
        // 准备
        var mockClient = new MockModbusClient();
        var wrapper = new ModbusClientWrapper(mockClient, _loggerMock.Object, DeviceId, _performanceMonitorMock.Object);

        const int threadCount = 10;
        const int operationsPerThread = 5;
        var totalOperations = threadCount * operationsPerThread;
        
        var completedOperations = new ConcurrentBag<int>();
        var exceptions = new ConcurrentBag<Exception>();
        var operationTimes = new ConcurrentBag<TimeSpan>();

        // 设置模拟客户端的响应
        mockClient.SetReadCoilsResponse(new byte[] { 0x55, 0x01 });
        mockClient.SetOperationDelay(TimeSpan.FromMilliseconds(10)); // 模拟网络延迟

        // 执行
        var tasks = Enumerable.Range(0, threadCount).Select(threadIndex =>
            Task.Run(async () =>
            {
                for (int i = 0; i < operationsPerThread; i++)
                {
                    try
                    {
                        var stopwatch = Stopwatch.StartNew();
                        
                        var result = await wrapper.ReadCoilsAsync(
                            (byte)(threadIndex + 1), 
                            (ushort)(i * 10), 
                            10, 
                            CancellationToken.None);
                        
                        stopwatch.Stop();
                        operationTimes.Add(stopwatch.Elapsed);
                        completedOperations.Add(threadIndex * operationsPerThread + i);
                        
                        // 验证结果
                        result.Length.Should().Be(2);
                    }
                    catch (Exception ex)
                    {
                        exceptions.Add(ex);
                    }
                }
            })).ToArray();

        await Task.WhenAll(tasks);

        // 验证
        exceptions.Should().BeEmpty("不应该有任何异常发生");
        completedOperations.Should().HaveCount(totalOperations, "所有操作都应该完成");
        mockClient.ReadCoilsCallCount.Should().Be(totalOperations, "应该调用正确次数的ReadCoils方法");
        
        // 验证串行执行（通过检查操作时间）
        var averageOperationTime = operationTimes.Average(t => t.TotalMilliseconds);
        averageOperationTime.Should().BeGreaterThan(8, "由于串行执行，平均操作时间应该接近设置的延迟时间");
    }

    /// <summary>
    /// 测试混合读写操作的线程安全�?    /// </summary>
    [Fact]
    public async Task MixedOperations_MultipleThreads_ShouldBeThreadSafe()
    {
        // 准备
        var mockClient = new MockModbusClient();
        var wrapper = new ModbusClientWrapper(mockClient, _loggerMock.Object, DeviceId, _performanceMonitorMock.Object);

        const int threadCount = 8;
        const int operationsPerThread = 3;
        
        var completedOperations = new ConcurrentBag<string>();
        var exceptions = new ConcurrentBag<Exception>();

        // 设置模拟客户端的响应
        mockClient.SetReadCoilsResponse(new byte[] { 0x55 });
        mockClient.SetReadHoldingRegistersResponse(new byte[] { 0x12, 0x34 });
        mockClient.SetOperationDelay(TimeSpan.FromMilliseconds(5));

        // 执行
        var tasks = Enumerable.Range(0, threadCount).Select(threadIndex =>
            Task.Run(async () =>
            {
                for (int i = 0; i < operationsPerThread; i++)
                {
                    try
                    {
                        var operationType = (threadIndex + i) % 4;
                        var operationId = $"Thread{threadIndex}_Op{i}_Type{operationType}";

                        switch (operationType)
                        {
                            case 0: // 读取线圈
                                await wrapper.ReadCoilsAsync(1, 0, 8, CancellationToken.None);
                                break;
                            case 1: // 读取保持寄存�?                                await wrapper.ReadHoldingRegistersAsync(1, 0, 1, CancellationToken.None);
                                break;
                            case 2: // 写入单个线圈
                                await wrapper.WriteSingleCoilAsync(1, 0, true, CancellationToken.None);
                                break;
                            case 3: // 写入单个寄存�?                                await wrapper.WriteSingleRegisterAsync(1, 0, 0x1234, CancellationToken.None);
                                break;
                        }

                        completedOperations.Add(operationId);
                    }
                    catch (Exception ex)
                    {
                        exceptions.Add(ex);
                    }
                }
            })).ToArray();

        await Task.WhenAll(tasks);

        // 验证
        exceptions.Should().BeEmpty("不应该有任何异常发生");
        completedOperations.Should().HaveCount(threadCount * operationsPerThread, "所有操作都应该完成");
        
        // 验证各种操作都被调用�?        mockClient.ReadCoilsCallCount.Should().BeGreaterThan(0);
        mockClient.ReadHoldingRegistersCallCount.Should().BeGreaterThan(0);
        mockClient.WriteSingleCoilCallCount.Should().BeGreaterThan(0);
        mockClient.WriteSingleRegisterCallCount.Should().BeGreaterThan(0);
    }

    /// <summary>
    /// 测试取消令牌的正确处�?    /// </summary>
    [Fact]
    public async Task ReadCoilsAsync_WithCancellation_ShouldRespectCancellationToken()
    {
        // 准备
        var mockClient = new MockModbusClient();
        var wrapper = new ModbusClientWrapper(mockClient, _loggerMock.Object, DeviceId, _performanceMonitorMock.Object);

        mockClient.SetReadCoilsResponse(new byte[] { 0x55 });
        mockClient.SetOperationDelay(TimeSpan.FromSeconds(1)); // 设置较长的延�?
        using var cts = new CancellationTokenSource();
        
        // 执行 - 启动操作然后立即取消
        var task = wrapper.ReadCoilsAsync(1, 0, 8, cts.Token);
        cts.Cancel();

        // 验证
        await Assert.ThrowsAsync<OperationCanceledException>(() => task);
    }

    /// <summary>
    /// 测试在高并发情况下的性能监控集成
    /// </summary>
    [Fact]
    public async Task PerformanceMonitoring_HighConcurrency_ShouldTrackAllOperations()
    {
        // 准备
        var mockClient = new MockModbusClient();
        var performanceMonitor = new ModbusPerformanceMonitor(_loggerMock.Object as ILogger<ModbusPerformanceMonitor>);
        var wrapper = new ModbusClientWrapper(mockClient, _loggerMock.Object, DeviceId, performanceMonitor);

        const int concurrentOperations = 20;
        mockClient.SetReadCoilsResponse(new byte[] { 0x55 });
        mockClient.SetOperationDelay(TimeSpan.FromMilliseconds(1));

        // 执行
        var tasks = Enumerable.Range(0, concurrentOperations).Select(i =>
            wrapper.ReadCoilsAsync(1, (ushort)i, 1, CancellationToken.None)
        ).ToArray();

        await Task.WhenAll(tasks);

        // 验证性能监控
        var metrics = performanceMonitor.GetDeviceMetrics(DeviceId);
        metrics.Should().NotBeNull();
        metrics!.TotalRequests.Should().Be(concurrentOperations);
        metrics.SuccessfulRequests.Should().Be(concurrentOperations);
        metrics.FailedRequests.Should().Be(0);
        metrics.SuccessRate.Should().Be(100.0);
    }

    /// <summary>
    /// 测试异常情况下的线程安全�?    /// </summary>
    [Fact]
    public async Task ExceptionHandling_MultipleThreads_ShouldBeThreadSafe()
    {
        // 准备
        var mockClient = new MockModbusClient();
        var wrapper = new ModbusClientWrapper(mockClient, _loggerMock.Object, DeviceId, _performanceMonitorMock.Object);

        const int threadCount = 5;
        const int operationsPerThread = 4;
        
        var exceptions = new ConcurrentBag<Exception>();
        var successfulOperations = new ConcurrentBag<int>();

        // 设置模拟客户端在某些情况下抛出异�?        mockClient.SetShouldThrowException(true);
        mockClient.SetExceptionProbability(0.3); // 30%的概率抛出异�?
        // 执行
        var tasks = Enumerable.Range(0, threadCount).Select(threadIndex =>
            Task.Run(async () =>
            {
                for (int i = 0; i < operationsPerThread; i++)
                {
                    try
                    {
                        await wrapper.ReadCoilsAsync(1, 0, 8, CancellationToken.None);
                        successfulOperations.Add(threadIndex * operationsPerThread + i);
                    }
                    catch (Exception ex)
                    {
                        exceptions.Add(ex);
                    }
                }
            })).ToArray();

        await Task.WhenAll(tasks);

        // 验证
        var totalOperations = threadCount * operationsPerThread;
        (successfulOperations.Count + exceptions.Count).Should().Be(totalOperations, "所有操作都应该完成（成功或失败�?);
        
        // 验证异常不会影响其他线程的操�?        successfulOperations.Should().NotBeEmpty("应该有一些操作成�?);
        exceptions.Should().NotBeEmpty("应该有一些操作失�?);
    }

    /// <summary>
    /// 测试资源释放的线程安全�?    /// </summary>
    [Fact]
    public async Task Dispose_WithConcurrentOperations_ShouldBeThreadSafe()
    {
        // 准备
        var mockClient = new MockModbusClient();
        var wrapper = new ModbusClientWrapper(mockClient, _loggerMock.Object, DeviceId, _performanceMonitorMock.Object);

        mockClient.SetReadCoilsResponse(new byte[] { 0x55 });
        mockClient.SetOperationDelay(TimeSpan.FromMilliseconds(50));

        // 启动一些长时间运行的操�?        var longRunningTasks = Enumerable.Range(0, 5).Select(i =>
            Task.Run(async () =>
            {
                try
                {
                    await wrapper.ReadCoilsAsync(1, 0, 8, CancellationToken.None);
                }
                catch (ObjectDisposedException)
                {
                    // 预期的异常，因为对象可能在操作过程中被释�?                }
                catch (Exception)
                {
                    // 其他异常也是可以接受�?                }
            })
        ).ToArray();

        // 等待一小段时间让操作开�?        await Task.Delay(10);

        // 执行 - 释放资源
        wrapper.Dispose();

        // 等待所有任务完�?        await Task.WhenAll(longRunningTasks);

        // 验证 - 释放后的操作应该失败
        await Assert.ThrowsAsync<ObjectDisposedException>(() => 
            wrapper.ReadCoilsAsync(1, 0, 8, CancellationToken.None));
    }

    /// <summary>
    /// 压力测试：大量并发操�?    /// </summary>
    [Fact]
    public async Task StressTest_HighConcurrency_ShouldMaintainThreadSafety()
    {
        // 准备
        var mockClient = new MockModbusClient();
        var wrapper = new ModbusClientWrapper(mockClient, _loggerMock.Object, DeviceId, _performanceMonitorMock.Object);

        const int concurrentTasks = 50;
        const int operationsPerTask = 10;
        
        var allOperationsCompleted = new ConcurrentBag<bool>();
        var exceptions = new ConcurrentBag<Exception>();

        mockClient.SetReadCoilsResponse(new byte[] { 0x55 });
        mockClient.SetOperationDelay(TimeSpan.FromMilliseconds(1));

        var stopwatch = Stopwatch.StartNew();

        // 执行
        var tasks = Enumerable.Range(0, concurrentTasks).Select(taskIndex =>
            Task.Run(async () =>
            {
                for (int i = 0; i < operationsPerTask; i++)
                {
                    try
                    {
                        await wrapper.ReadCoilsAsync(
                            (byte)(taskIndex % 10 + 1), 
                            (ushort)(i % 100), 
                            (ushort)(i % 10 + 1), 
                            CancellationToken.None);
                        
                        allOperationsCompleted.Add(true);
                    }
                    catch (Exception ex)
                    {
                        exceptions.Add(ex);
                    }
                }
            })).ToArray();

        await Task.WhenAll(tasks);
        stopwatch.Stop();

        // 验证
        var totalExpectedOperations = concurrentTasks * operationsPerTask;
        exceptions.Should().BeEmpty("压力测试中不应该有异�?);
        allOperationsCompleted.Should().HaveCount(totalExpectedOperations, "所有操作都应该完成");
        mockClient.ReadCoilsCallCount.Should().Be(totalExpectedOperations);
        
        // 性能验证 - 确保串行执行没有造成过度的性能损失
        var averageTimePerOperation = stopwatch.ElapsedMilliseconds / (double)totalExpectedOperations;
        averageTimePerOperation.Should().BeLessThan(50, "平均每个操作的时间应该在合理范围�?);
        
        _loggerMock.Verify(
            x => x.Log(
                LogLevel.Debug,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("开始读取线�?)),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Exactly(totalExpectedOperations));
    }
}
