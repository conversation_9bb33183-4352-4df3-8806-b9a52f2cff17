using Gsdt.ModbusGateway.Models;
using Gsdt.ModbusGateway.Services;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Xunit;
using FluentAssertions;
using System.Net;

namespace Gsdt.ModbusGateway.Tests.Services;

/// <summary>
/// ModbusTcpGatewayService 测试类
/// </summary>
public class ModbusTcpGatewayServiceTests
{
    private readonly Mock<ILogger<ModbusTcpGatewayService>> _loggerMock;
    private readonly Mock<IOptions<GatewayConfig>> _configMock;
    private readonly Mock<IModbusClientFactory> _clientFactoryMock;
    private readonly Mock<IModbusRequestHandler> _modbusRequestHandlerMock;
    private readonly GatewayConfig _config;

    public ModbusTcpGatewayServiceTests()
    {
        _loggerMock = new Mock<ILogger<ModbusTcpGatewayService>>();
        _clientFactoryMock = new Mock<IModbusClientFactory>();
        _modbusRequestHandlerMock = new Mock<IModbusRequestHandler>();

        // 配置测试用的网关设置
        _config = new GatewayConfig
        {
            TcpTargetDevices =
            [
                new TcpTargetDevice
                {
                    Id = 1,
                    Ip = "127.0.0.1",
                    Port = 503,
                    Timeout = 1000
                }
            ],
            Routes =
            [
                new RouteConfig
                {
                    Port = 502,
                    Transforms =
                    [
                        new TransformRout()
                        {
                            TargetDeviceId = 1,
                            TargetStartAddress = 0x0000,
                            SourceStartAddress = 0x0000,
                            Quantity = 10
                        }
                    ]
                }
            ]
        };

        _configMock = new Mock<IOptions<GatewayConfig>>();
        _configMock.Setup(x => x.Value).Returns(_config);
    }

    /// <summary>
    /// 测试启动服务时是否正确初始化Modbus客户端
    /// </summary>
    [Fact]
    public async Task StartAsync_ShouldInitializeModbusClients()
    {
        // 准备 - 创建真实的 ThreadSafeModbusClientPool 实例而不是 Mock
        var poolLogger = new Mock<ILogger<ThreadSafeModbusClientPool>>();
        var clientPool = new ThreadSafeModbusClientPool(poolLogger.Object);

        var service = new ModbusTcpGatewayService(
            _loggerMock.Object,
            _configMock.Object,
            _clientFactoryMock.Object,
            _modbusRequestHandlerMock.Object,
            clientPool);

        var tcpClient = new Mock<ModbusTcpClient>();
        _clientFactoryMock.Setup(f => f.CreateTcpClient(It.IsAny<TargetDevice>()))
            .Returns(tcpClient.Object);

        // 执行
        await service.StartAsync(CancellationToken.None);

        // 验证
        _clientFactoryMock.Verify(f => f.CreateTcpClient(It.Is<TargetDevice>(d => d.Id == 1)), Times.Once);
        
        // 清理资源
        clientPool.Dispose();
    }
}
