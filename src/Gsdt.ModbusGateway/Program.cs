using System.Runtime.InteropServices;
using System.Text.Json;
using Gsdt.ModbusGateway;
using Gsdt.ModbusGateway.Models;
using Gsdt.ModbusGateway.Services;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Serilog;
using Serilog.Settings.Configuration;

if (!RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
{
    Console.WriteLine("警告：此应用程序主要在Windows平台上测试。");
}

// 加载配置文件
var configuration = new ConfigurationBuilder()
    .SetBasePath(AppContext.BaseDirectory) // 控制台需显式设置路径[5](@ref)
    .AddJsonFile("appsettings.json")
    .Build();

var readerOptions = new ConfigurationReaderOptions(
    typeof(ConsoleLoggerConfigurationExtensions).Assembly,
    typeof(FileLoggerConfigurationExtensions).Assembly);

// 配置 Serilog（AOT 兼容方式）
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(configuration, readerOptions)
    .CreateLogger();

var builder = WebApplication.CreateBuilder(args);

// 添加配置
builder.Configuration.AddConfiguration(configuration);

// 配置 Serilog
builder.Host.UseSerilog();

// 添加服务
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// 配置 JSON 序列化选项
builder.Services.Configure<JsonSerializerOptions>(options =>
{
    options.TypeInfoResolver = AppJsonContext.Default;
    options.PropertyNameCaseInsensitive = true;
    options.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
    options.WriteIndented = true;
});

var gatewayConfig = new GatewayConfig();
var gatewayConfigSection = builder.Configuration.GetSection("GatewayConfig");
if (gatewayConfigSection == null)
{
    throw new Exception("未找到GatewayConfig配置节");
}

gatewayConfig.TcpTargetDevices = gatewayConfigSection.GetSection("TcpTargetDevices").Get<List<TcpTargetDevice>>() ?? [];
gatewayConfig.RtuTargetDevices = gatewayConfigSection.GetSection("RtuTargetDevices").Get<List<RtuTargetDevice>>() ?? [];
gatewayConfig.Routes = gatewayConfigSection.GetSection("Routes").Get<List<RouteConfig>>() ?? [];

builder.Services.AddSingleton(Options.Create(gatewayConfig));

// 注册配置验证服务
builder.Services.AddSingleton<IConfigValidationService, ConfigValidationService>();

// 注册核心服务
builder.Services.AddSingleton<IModbusClientFactory, ModbusClientFactory>();
builder.Services.AddSingleton<IModbusRequestHandler, ModbusRequestHandler>();

// 注册监控和健康检查服务
builder.Services.AddSingleton<ModbusPerformanceMonitor>();
builder.Services.AddSingleton<ModbusConnectionHealthService>();

// 注册线程安全组件（依赖于性能监控服务）
builder.Services.AddSingleton<ThreadSafeModbusClientPool>(provider =>
{
    var logger = provider.GetRequiredService<ILogger<ThreadSafeModbusClientPool>>();
    var performanceMonitor = provider.GetRequiredService<ModbusPerformanceMonitor>();
    return new ThreadSafeModbusClientPool(logger, performanceMonitor);
});

// 注册后台服务
builder.Services.AddHostedService<ModbusTcpGatewayService>();
builder.Services.AddHostedService<ModbusConnectionHealthService>();

var app = builder.Build();

// 在启动前验证配置
ValidateConfiguration(app.Services);

// 配置中间件管道
// 在所有环境中启用Swagger以便于API访问
app.UseSwagger();
app.UseSwaggerUI(c =>
{
    c.SwaggerEndpoint("/swagger/v1/swagger.json", "Modbus Gateway API v1");
    c.RoutePrefix = "swagger"; // 设置Swagger UI的路径为 /swagger
});

app.UseRouting();
app.MapControllers();

// 添加一个简单的根路径响应
app.MapGet("/", () => "Modbus TCP Gateway v1.0.0 - Running");

await app.RunAsync();
return;

// 配置验证方法
static void ValidateConfiguration(IServiceProvider services)
{
    var logger = services.GetRequiredService<ILogger<Program>>();
    var configValidationService = services.GetRequiredService<IConfigValidationService>();
    var config = services.GetRequiredService<IOptions<GatewayConfig>>().Value;

    try
    {
        logger.LogInformation("正在验证配置...");
        configValidationService.ValidateConfig(config);
        logger.LogInformation("配置验证通过");
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "配置验证失败");
        throw; // 重新抛出异常以终止应用程序
    }
}