2025-05-26 11:25:26.099 +08:00 [INF] 正在验证配置...
2025-05-26 11:25:26.129 +08:00 [INF] 配置验证通过
2025-05-26 11:25:26.195 +08:00 [INF] 正在启动Modbus TCP网关服务...
2025-05-26 11:25:26.198 +08:00 [INF] 创建Modbus客户端：ID=1001, IP=***************, Port=8080
2025-05-26 11:25:26.204 +08:00 [DBG] 已注册设备 1001 的线程安全客户端包装器
2025-05-26 11:25:26.204 +08:00 [INF] 已注册设备 1001 到线程安全连接池
2025-05-26 11:25:26.204 +08:00 [INF] 创建Modbus客户端：ID=1002, IP=***************, Port=8080
2025-05-26 11:25:26.205 +08:00 [DBG] 已注册设备 1002 的线程安全客户端包装器
2025-05-26 11:25:26.205 +08:00 [INF] 已注册设备 1002 到线程安全连接池
2025-05-26 11:25:26.206 +08:00 [INF] 创建Modbus客户端：ID=1003, IP=***************, Port=8080
2025-05-26 11:25:26.206 +08:00 [DBG] 已注册设备 1003 的线程安全客户端包装器
2025-05-26 11:25:26.206 +08:00 [INF] 已注册设备 1003 到线程安全连接池
2025-05-26 11:25:26.207 +08:00 [INF] 创建Modbus客户端：ID=1004, IP=***************, Port=8080
2025-05-26 11:25:26.207 +08:00 [DBG] 已注册设备 1004 的线程安全客户端包装器
2025-05-26 11:25:26.207 +08:00 [INF] 已注册设备 1004 到线程安全连接池
2025-05-26 11:25:26.208 +08:00 [INF] 创建Modbus客户端：ID=1005, IP=***************, Port=8080
2025-05-26 11:25:26.209 +08:00 [DBG] 已注册设备 1005 的线程安全客户端包装器
2025-05-26 11:25:26.209 +08:00 [INF] 已注册设备 1005 到线程安全连接池
2025-05-26 11:25:26.209 +08:00 [INF] 创建Modbus客户端：ID=1006, IP=***************, Port=8080
2025-05-26 11:25:26.210 +08:00 [DBG] 已注册设备 1006 的线程安全客户端包装器
2025-05-26 11:25:26.210 +08:00 [INF] 已注册设备 1006 到线程安全连接池
2025-05-26 11:25:26.210 +08:00 [INF] 创建Modbus客户端：ID=1007, IP=***************, Port=8080
2025-05-26 11:25:26.211 +08:00 [DBG] 已注册设备 1007 的线程安全客户端包装器
2025-05-26 11:25:26.211 +08:00 [INF] 已注册设备 1007 到线程安全连接池
2025-05-26 11:25:26.211 +08:00 [INF] 创建Modbus客户端：ID=1008, IP=***************, Port=8080
2025-05-26 11:25:26.212 +08:00 [DBG] 已注册设备 1008 的线程安全客户端包装器
2025-05-26 11:25:26.212 +08:00 [INF] 已注册设备 1008 到线程安全连接池
2025-05-26 11:25:26.213 +08:00 [INF] 创建Modbus客户端：ID=1009, IP=***************, Port=8080
2025-05-26 11:25:26.213 +08:00 [DBG] 已注册设备 1009 的线程安全客户端包装器
2025-05-26 11:25:26.213 +08:00 [INF] 已注册设备 1009 到线程安全连接池
2025-05-26 11:25:26.214 +08:00 [INF] 创建Modbus客户端：ID=1010, IP=***************, Port=8080
2025-05-26 11:25:26.215 +08:00 [DBG] 已注册设备 1010 的线程安全客户端包装器
2025-05-26 11:25:26.215 +08:00 [INF] 已注册设备 1010 到线程安全连接池
2025-05-26 11:25:26.216 +08:00 [INF] 创建Modbus客户端并连接到设备：ID=2001, PortName=COM3, BaudRate=9600
2025-05-26 11:25:26.225 +08:00 [DBG] 已注册设备 2001 的线程安全客户端包装器
2025-05-26 11:25:26.225 +08:00 [INF] 已注册设备 2001 到线程安全连接池
2025-05-26 11:25:26.231 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8080
2025-05-26 11:25:26.232 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8081
2025-05-26 11:25:26.233 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8082
2025-05-26 11:25:26.233 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8083
2025-05-26 11:25:26.233 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8084
2025-05-26 11:25:26.234 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8085
2025-05-26 11:25:26.234 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8086
2025-05-26 11:25:26.234 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8087
2025-05-26 11:25:26.234 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8088
2025-05-26 11:25:26.235 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8089
2025-05-26 11:25:26.237 +08:00 [INF] Modbus连接健康检查服务已启动
2025-05-26 11:25:26.240 +08:00 [DBG] 开始监控请求: RequestId=1b401252, DeviceId=1001, FunctionCode=0
2025-05-26 11:25:26.242 +08:00 [WRN] 设备 1001 健康检查失败，连续失败次数: 1
2025-05-26 11:25:26.244 +08:00 [DBG] 完成请求监控: RequestId=1b401252, Success=false, TotalTime=4.0398ms, ExecutionTime=4.0398ms
2025-05-26 11:25:26.245 +08:00 [WRN] 请求失败: RequestId=1b401252, DeviceId=1001, Error=Health check failed
2025-05-26 11:25:26.246 +08:00 [DBG] 开始监控请求: RequestId=095dad18, DeviceId=1002, FunctionCode=0
2025-05-26 11:25:26.246 +08:00 [WRN] 设备 1002 健康检查失败，连续失败次数: 1
2025-05-26 11:25:26.247 +08:00 [DBG] 完成请求监控: RequestId=095dad18, Success=false, TotalTime=0.6691ms, ExecutionTime=0.6691ms
2025-05-26 11:25:26.247 +08:00 [WRN] 请求失败: RequestId=095dad18, DeviceId=1002, Error=Health check failed
2025-05-26 11:25:26.248 +08:00 [DBG] 开始监控请求: RequestId=5094be3c, DeviceId=1003, FunctionCode=0
2025-05-26 11:25:26.248 +08:00 [WRN] 设备 1003 健康检查失败，连续失败次数: 1
2025-05-26 11:25:26.248 +08:00 [DBG] 完成请求监控: RequestId=5094be3c, Success=false, TotalTime=0.5994ms, ExecutionTime=0.5994ms
2025-05-26 11:25:26.248 +08:00 [WRN] 请求失败: RequestId=5094be3c, DeviceId=1003, Error=Health check failed
2025-05-26 11:25:26.249 +08:00 [DBG] 开始监控请求: RequestId=2f518ef4, DeviceId=1004, FunctionCode=0
2025-05-26 11:25:26.249 +08:00 [WRN] 设备 1004 健康检查失败，连续失败次数: 1
2025-05-26 11:25:26.250 +08:00 [DBG] 完成请求监控: RequestId=2f518ef4, Success=false, TotalTime=1.2071ms, ExecutionTime=1.2071ms
2025-05-26 11:25:26.250 +08:00 [WRN] 请求失败: RequestId=2f518ef4, DeviceId=1004, Error=Health check failed
2025-05-26 11:25:26.251 +08:00 [DBG] 开始监控请求: RequestId=5373e1b3, DeviceId=1005, FunctionCode=0
2025-05-26 11:25:26.251 +08:00 [WRN] 设备 1005 健康检查失败，连续失败次数: 1
2025-05-26 11:25:26.252 +08:00 [DBG] 完成请求监控: RequestId=5373e1b3, Success=false, TotalTime=0.6114ms, ExecutionTime=0.6114ms
2025-05-26 11:25:26.252 +08:00 [WRN] 请求失败: RequestId=5373e1b3, DeviceId=1005, Error=Health check failed
2025-05-26 11:25:26.252 +08:00 [DBG] 开始监控请求: RequestId=ee063362, DeviceId=1006, FunctionCode=0
2025-05-26 11:25:26.252 +08:00 [WRN] 设备 1006 健康检查失败，连续失败次数: 1
2025-05-26 11:25:26.253 +08:00 [DBG] 完成请求监控: RequestId=ee063362, Success=false, TotalTime=0.5935ms, ExecutionTime=0.5935ms
2025-05-26 11:25:26.253 +08:00 [WRN] 请求失败: RequestId=ee063362, DeviceId=1006, Error=Health check failed
2025-05-26 11:25:26.254 +08:00 [DBG] 开始监控请求: RequestId=9ec728ec, DeviceId=1007, FunctionCode=0
2025-05-26 11:25:26.254 +08:00 [WRN] 设备 1007 健康检查失败，连续失败次数: 1
2025-05-26 11:25:26.255 +08:00 [DBG] 完成请求监控: RequestId=9ec728ec, Success=false, TotalTime=0.9587ms, ExecutionTime=0.9587ms
2025-05-26 11:25:26.255 +08:00 [WRN] 请求失败: RequestId=9ec728ec, DeviceId=1007, Error=Health check failed
2025-05-26 11:25:26.256 +08:00 [DBG] 开始监控请求: RequestId=ec488f1f, DeviceId=1008, FunctionCode=0
2025-05-26 11:25:26.256 +08:00 [WRN] 设备 1008 健康检查失败，连续失败次数: 1
2025-05-26 11:25:26.256 +08:00 [DBG] 完成请求监控: RequestId=ec488f1f, Success=false, TotalTime=0.6029ms, ExecutionTime=0.6029ms
2025-05-26 11:25:26.256 +08:00 [WRN] 请求失败: RequestId=ec488f1f, DeviceId=1008, Error=Health check failed
2025-05-26 11:25:26.257 +08:00 [DBG] 开始监控请求: RequestId=47f3ede1, DeviceId=1009, FunctionCode=0
2025-05-26 11:25:26.257 +08:00 [WRN] 设备 1009 健康检查失败，连续失败次数: 1
2025-05-26 11:25:26.258 +08:00 [DBG] 完成请求监控: RequestId=47f3ede1, Success=false, TotalTime=0.7742ms, ExecutionTime=0.7742ms
2025-05-26 11:25:26.258 +08:00 [WRN] 请求失败: RequestId=47f3ede1, DeviceId=1009, Error=Health check failed
2025-05-26 11:25:26.259 +08:00 [DBG] 开始监控请求: RequestId=1a6d2422, DeviceId=1010, FunctionCode=0
2025-05-26 11:25:26.259 +08:00 [WRN] 设备 1010 健康检查失败，连续失败次数: 1
2025-05-26 11:25:26.260 +08:00 [DBG] 完成请求监控: RequestId=1a6d2422, Success=false, TotalTime=0.7479ms, ExecutionTime=0.7479ms
2025-05-26 11:25:26.260 +08:00 [WRN] 请求失败: RequestId=1a6d2422, DeviceId=1010, Error=Health check failed
2025-05-26 11:25:26.261 +08:00 [DBG] 开始监控请求: RequestId=df748356, DeviceId=2001, FunctionCode=0
2025-05-26 11:25:26.261 +08:00 [DBG] 开始读取保持寄存器: SlaveId=255, StartAddress=0, Quantity=1
2025-05-26 11:25:26.286 +08:00 [FTL] Application startup exception
System.NotSupportedException: JsonTypeInfo metadata for type '<>f__AnonymousType6`5[System.String,System.String,System.String,System.DateTime,<>f__AnonymousType7`5[System.String,System.String,System.String,System.String,System.String]]' was not provided by TypeInfoResolver of type '[]'. If using source generation, ensure that all root types passed to the serializer have been annotated with 'JsonSerializableAttribute', along with any types that might be serialized polymorphically.
   at System.Text.Json.ThrowHelper.ThrowNotSupportedException_NoMetadataForType(Type type, IJsonTypeInfoResolver resolver)
   at System.Text.Json.JsonSerializerOptions.GetTypeInfoInternal(Type type, Boolean ensureConfigured, Nullable`1 ensureNotNull, Boolean resolveIfMutable, Boolean fallBackToNearestAncestorType)
   at System.Text.Json.JsonSerializerOptions.GetTypeInfo(Type type)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.AddResponseWritingToMethodCall(Expression methodCall, Type returnType, RequestDelegateFactoryContext factoryContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.CreateTargetableRequestDelegate(MethodInfo methodInfo, Expression targetExpression, RequestDelegateFactoryContext factoryContext, Expression`1 targetFactory)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.Create(Delegate handler, RequestDelegateFactoryOptions options, RequestDelegateMetadataResult metadataResult)
   at Microsoft.AspNetCore.Routing.RouteEndpointDataSource.CreateRouteEndpointBuilder(RouteEntry entry, RoutePattern groupPrefix, IReadOnlyList`1 groupConventions, IReadOnlyList`1 groupFinallyConventions)
   at Microsoft.AspNetCore.Routing.RouteEndpointDataSource.get_Endpoints()
   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.CreateEndpointsUnsynchronized()
   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.EnsureEndpointsInitialized()
   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.get_Endpoints()
   at Microsoft.AspNetCore.Routing.DataSourceDependentCache`1.Initialize()
   at System.Threading.LazyInitializer.EnsureInitializedCore[T](T& target, Boolean& initialized, Object& syncLock, Func`1 valueFactory)
   at Microsoft.AspNetCore.Routing.DataSourceDependentCache`1.EnsureInitialized()
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationPolicyCache..ctor(EndpointDataSource dataSource)
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Void** arguments, Signature sig, Boolean isConstructor)
   at System.Reflection.MethodBaseInvoker.InvokeDirectByRefWithFewArgs(Object obj, Span`1 copyOfArgs, BindingFlags invokeAttr)
   at System.Reflection.MethodBaseInvoker.InvokeWithOneArg(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at System.Collections.Concurrent.ConcurrentDictionary`2.GetOrAdd(TKey key, Func`2 valueFactory)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetService[T](IServiceProvider provider)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware..ctor(RequestDelegate next, IAuthorizationPolicyProvider policyProvider, IServiceProvider services)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddlewareInternal..ctor(RequestDelegate next, IServiceProvider services, IAuthorizationPolicyProvider policyProvider, ILogger`1 logger)
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Void** arguments, Signature sig, Boolean isConstructor)
   at System.Reflection.MethodBaseInvoker.InvokeDirectByRefWithFewArgs(Object obj, Span`1 copyOfArgs, BindingFlags invokeAttr)
   at System.Reflection.MethodBaseInvoker.InvokeWithFewArgs(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.Internal.ActivatorUtilities.ConstructorMatcher.CreateInstance(IServiceProvider provider)
   at Microsoft.Extensions.Internal.ActivatorUtilities.CreateInstance(IServiceProvider provider, Type instanceType, Object[] parameters)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.ReflectionMiddlewareBinder.CreateMiddleware(RequestDelegate next)
   at Microsoft.AspNetCore.Builder.ApplicationBuilder.Build()
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
2025-05-26 11:25:26.298 +08:00 [ERR] Hosting failed to start
System.NotSupportedException: JsonTypeInfo metadata for type '<>f__AnonymousType6`5[System.String,System.String,System.String,System.DateTime,<>f__AnonymousType7`5[System.String,System.String,System.String,System.String,System.String]]' was not provided by TypeInfoResolver of type '[]'. If using source generation, ensure that all root types passed to the serializer have been annotated with 'JsonSerializableAttribute', along with any types that might be serialized polymorphically.
   at System.Text.Json.ThrowHelper.ThrowNotSupportedException_NoMetadataForType(Type type, IJsonTypeInfoResolver resolver)
   at System.Text.Json.JsonSerializerOptions.GetTypeInfoInternal(Type type, Boolean ensureConfigured, Nullable`1 ensureNotNull, Boolean resolveIfMutable, Boolean fallBackToNearestAncestorType)
   at System.Text.Json.JsonSerializerOptions.GetTypeInfo(Type type)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.AddResponseWritingToMethodCall(Expression methodCall, Type returnType, RequestDelegateFactoryContext factoryContext)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.CreateTargetableRequestDelegate(MethodInfo methodInfo, Expression targetExpression, RequestDelegateFactoryContext factoryContext, Expression`1 targetFactory)
   at Microsoft.AspNetCore.Http.RequestDelegateFactory.Create(Delegate handler, RequestDelegateFactoryOptions options, RequestDelegateMetadataResult metadataResult)
   at Microsoft.AspNetCore.Routing.RouteEndpointDataSource.CreateRouteEndpointBuilder(RouteEntry entry, RoutePattern groupPrefix, IReadOnlyList`1 groupConventions, IReadOnlyList`1 groupFinallyConventions)
   at Microsoft.AspNetCore.Routing.RouteEndpointDataSource.get_Endpoints()
   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.CreateEndpointsUnsynchronized()
   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.EnsureEndpointsInitialized()
   at Microsoft.AspNetCore.Routing.CompositeEndpointDataSource.get_Endpoints()
   at Microsoft.AspNetCore.Routing.DataSourceDependentCache`1.Initialize()
   at System.Threading.LazyInitializer.EnsureInitializedCore[T](T& target, Boolean& initialized, Object& syncLock, Func`1 valueFactory)
   at Microsoft.AspNetCore.Routing.DataSourceDependentCache`1.EnsureInitialized()
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationPolicyCache..ctor(EndpointDataSource dataSource)
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Void** arguments, Signature sig, Boolean isConstructor)
   at System.Reflection.MethodBaseInvoker.InvokeDirectByRefWithFewArgs(Object obj, Span`1 copyOfArgs, BindingFlags invokeAttr)
   at System.Reflection.MethodBaseInvoker.InvokeWithOneArg(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitConstructor(ConstructorCallSite constructorCallSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitRootCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at System.Collections.Concurrent.ConcurrentDictionary`2.GetOrAdd(TKey key, Func`2 valueFactory)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetService[T](IServiceProvider provider)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware..ctor(RequestDelegate next, IAuthorizationPolicyProvider policyProvider, IServiceProvider services)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddlewareInternal..ctor(RequestDelegate next, IServiceProvider services, IAuthorizationPolicyProvider policyProvider, ILogger`1 logger)
   at System.RuntimeMethodHandle.InvokeMethod(Object target, Void** arguments, Signature sig, Boolean isConstructor)
   at System.Reflection.MethodBaseInvoker.InvokeDirectByRefWithFewArgs(Object obj, Span`1 copyOfArgs, BindingFlags invokeAttr)
   at System.Reflection.MethodBaseInvoker.InvokeWithFewArgs(Object obj, BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at System.Reflection.RuntimeConstructorInfo.Invoke(BindingFlags invokeAttr, Binder binder, Object[] parameters, CultureInfo culture)
   at Microsoft.Extensions.Internal.ActivatorUtilities.ConstructorMatcher.CreateInstance(IServiceProvider provider)
   at Microsoft.Extensions.Internal.ActivatorUtilities.CreateInstance(IServiceProvider provider, Type instanceType, Object[] parameters)
   at Microsoft.AspNetCore.Builder.UseMiddlewareExtensions.ReflectionMiddlewareBinder.CreateMiddleware(RequestDelegate next)
   at Microsoft.AspNetCore.Builder.ApplicationBuilder.Build()
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__14_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2025-05-26 11:25:27.378 +08:00 [DBG] 正在释放线程安全ModbusClient连接池资源
2025-05-26 11:25:27.379 +08:00 [DBG] 线程安全ModbusClient连接池资源已释放
2025-05-26 11:25:27.379 +08:00 [INF] 正在输出最终性能报告...
2025-05-26 11:25:27.381 +08:00 [INF] === Modbus性能报告 ===
2025-05-26 11:25:27.384 +08:00 [INF] 设备 1001: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:25:27.386 +08:00 [WRN] 设备 1001 最近错误: Health check failed
2025-05-26 11:25:27.386 +08:00 [INF] 设备 1002: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:25:27.388 +08:00 [WRN] 设备 1002 最近错误: Health check failed
2025-05-26 11:25:27.389 +08:00 [INF] 设备 1003: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:25:27.390 +08:00 [WRN] 设备 1003 最近错误: Health check failed
2025-05-26 11:25:27.391 +08:00 [INF] 设备 1004: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:25:27.392 +08:00 [WRN] 设备 1004 最近错误: Health check failed
2025-05-26 11:25:27.393 +08:00 [INF] 设备 1005: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:25:27.394 +08:00 [WRN] 设备 1005 最近错误: Health check failed
2025-05-26 11:25:27.395 +08:00 [INF] 设备 1006: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:25:27.396 +08:00 [WRN] 设备 1006 最近错误: Health check failed
2025-05-26 11:25:27.396 +08:00 [INF] 设备 1007: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:25:27.398 +08:00 [WRN] 设备 1007 最近错误: Health check failed
2025-05-26 11:25:27.398 +08:00 [INF] 设备 1008: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:25:27.399 +08:00 [WRN] 设备 1008 最近错误: Health check failed
2025-05-26 11:25:27.400 +08:00 [INF] 设备 1009: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:25:27.402 +08:00 [WRN] 设备 1009 最近错误: Health check failed
2025-05-26 11:25:27.403 +08:00 [INF] 设备 1010: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:25:27.404 +08:00 [WRN] 设备 1010 最近错误: Health check failed
2025-05-26 11:25:27.405 +08:00 [INF] 设备 2001: 总请求=1, 成功=0, 失败=0, 活跃请求=1, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:25:27.406 +08:00 [INF] === 性能报告结束 ===
2025-05-26 11:25:28.617 +08:00 [DBG] 设备 2001 健康检查失败: Cannot access a disposed object.
Object name: 'System.Threading.SemaphoreSlim'.
2025-05-26 11:25:28.617 +08:00 [WRN] 设备 2001 健康检查失败，连续失败次数: 1
2025-05-26 11:25:28.618 +08:00 [DBG] 完成请求监控: RequestId=df748356, Success=false, TotalTime=2357.2584ms, ExecutionTime=2357.2584ms
2025-05-26 11:25:28.618 +08:00 [WRN] 请求失败: RequestId=df748356, DeviceId=2001, Error=Health check failed
2025-05-26 11:25:28.619 +08:00 [INF] 连接健康检查服务正在停止
2025-05-26 11:26:12.199 +08:00 [INF] 正在验证配置...
2025-05-26 11:26:12.229 +08:00 [INF] 配置验证通过
2025-05-26 11:26:12.280 +08:00 [INF] 正在启动Modbus TCP网关服务...
2025-05-26 11:26:12.283 +08:00 [INF] 创建Modbus客户端：ID=1001, IP=***************, Port=8080
2025-05-26 11:26:12.289 +08:00 [DBG] 已注册设备 1001 的线程安全客户端包装器
2025-05-26 11:26:12.289 +08:00 [INF] 已注册设备 1001 到线程安全连接池
2025-05-26 11:26:12.290 +08:00 [INF] 创建Modbus客户端：ID=1002, IP=***************, Port=8080
2025-05-26 11:26:12.291 +08:00 [DBG] 已注册设备 1002 的线程安全客户端包装器
2025-05-26 11:26:12.291 +08:00 [INF] 已注册设备 1002 到线程安全连接池
2025-05-26 11:26:12.291 +08:00 [INF] 创建Modbus客户端：ID=1003, IP=***************, Port=8080
2025-05-26 11:26:12.292 +08:00 [DBG] 已注册设备 1003 的线程安全客户端包装器
2025-05-26 11:26:12.292 +08:00 [INF] 已注册设备 1003 到线程安全连接池
2025-05-26 11:26:12.292 +08:00 [INF] 创建Modbus客户端：ID=1004, IP=***************, Port=8080
2025-05-26 11:26:12.293 +08:00 [DBG] 已注册设备 1004 的线程安全客户端包装器
2025-05-26 11:26:12.293 +08:00 [INF] 已注册设备 1004 到线程安全连接池
2025-05-26 11:26:12.294 +08:00 [INF] 创建Modbus客户端：ID=1005, IP=***************, Port=8080
2025-05-26 11:26:12.294 +08:00 [DBG] 已注册设备 1005 的线程安全客户端包装器
2025-05-26 11:26:12.294 +08:00 [INF] 已注册设备 1005 到线程安全连接池
2025-05-26 11:26:12.295 +08:00 [INF] 创建Modbus客户端：ID=1006, IP=***************, Port=8080
2025-05-26 11:26:12.296 +08:00 [DBG] 已注册设备 1006 的线程安全客户端包装器
2025-05-26 11:26:12.296 +08:00 [INF] 已注册设备 1006 到线程安全连接池
2025-05-26 11:26:12.296 +08:00 [INF] 创建Modbus客户端：ID=1007, IP=***************, Port=8080
2025-05-26 11:26:12.297 +08:00 [DBG] 已注册设备 1007 的线程安全客户端包装器
2025-05-26 11:26:12.297 +08:00 [INF] 已注册设备 1007 到线程安全连接池
2025-05-26 11:26:12.298 +08:00 [INF] 创建Modbus客户端：ID=1008, IP=***************, Port=8080
2025-05-26 11:26:12.298 +08:00 [DBG] 已注册设备 1008 的线程安全客户端包装器
2025-05-26 11:26:12.298 +08:00 [INF] 已注册设备 1008 到线程安全连接池
2025-05-26 11:26:12.299 +08:00 [INF] 创建Modbus客户端：ID=1009, IP=***************, Port=8080
2025-05-26 11:26:12.300 +08:00 [DBG] 已注册设备 1009 的线程安全客户端包装器
2025-05-26 11:26:12.300 +08:00 [INF] 已注册设备 1009 到线程安全连接池
2025-05-26 11:26:12.300 +08:00 [INF] 创建Modbus客户端：ID=1010, IP=***************, Port=8080
2025-05-26 11:26:12.301 +08:00 [DBG] 已注册设备 1010 的线程安全客户端包装器
2025-05-26 11:26:12.301 +08:00 [INF] 已注册设备 1010 到线程安全连接池
2025-05-26 11:26:12.302 +08:00 [INF] 创建Modbus客户端并连接到设备：ID=2001, PortName=COM3, BaudRate=9600
2025-05-26 11:26:12.307 +08:00 [DBG] 已注册设备 2001 的线程安全客户端包装器
2025-05-26 11:26:12.308 +08:00 [INF] 已注册设备 2001 到线程安全连接池
2025-05-26 11:26:12.312 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8080
2025-05-26 11:26:12.313 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8081
2025-05-26 11:26:12.314 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8082
2025-05-26 11:26:12.314 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8083
2025-05-26 11:26:12.315 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8084
2025-05-26 11:26:12.315 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8085
2025-05-26 11:26:12.316 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8086
2025-05-26 11:26:12.316 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8087
2025-05-26 11:26:12.317 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8088
2025-05-26 11:26:12.317 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8089
2025-05-26 11:26:12.318 +08:00 [INF] Modbus连接健康检查服务已启动
2025-05-26 11:26:12.321 +08:00 [DBG] 开始监控请求: RequestId=8cf158d7, DeviceId=1001, FunctionCode=0
2025-05-26 11:26:12.322 +08:00 [WRN] 设备 1001 健康检查失败，连续失败次数: 1
2025-05-26 11:26:12.323 +08:00 [DBG] 完成请求监控: RequestId=8cf158d7, Success=false, TotalTime=2.0914ms, ExecutionTime=2.0914ms
2025-05-26 11:26:12.324 +08:00 [WRN] 请求失败: RequestId=8cf158d7, DeviceId=1001, Error=Health check failed
2025-05-26 11:26:12.325 +08:00 [DBG] 开始监控请求: RequestId=7839e8af, DeviceId=1002, FunctionCode=0
2025-05-26 11:26:12.325 +08:00 [WRN] 设备 1002 健康检查失败，连续失败次数: 1
2025-05-26 11:26:12.325 +08:00 [DBG] 完成请求监控: RequestId=7839e8af, Success=false, TotalTime=0.6631ms, ExecutionTime=0.6631ms
2025-05-26 11:26:12.325 +08:00 [WRN] 请求失败: RequestId=7839e8af, DeviceId=1002, Error=Health check failed
2025-05-26 11:26:12.326 +08:00 [DBG] 开始监控请求: RequestId=8335cb85, DeviceId=1003, FunctionCode=0
2025-05-26 11:26:12.326 +08:00 [WRN] 设备 1003 健康检查失败，连续失败次数: 1
2025-05-26 11:26:12.327 +08:00 [DBG] 完成请求监控: RequestId=8335cb85, Success=false, TotalTime=0.5605ms, ExecutionTime=0.5605ms
2025-05-26 11:26:12.327 +08:00 [WRN] 请求失败: RequestId=8335cb85, DeviceId=1003, Error=Health check failed
2025-05-26 11:26:12.327 +08:00 [DBG] 开始监控请求: RequestId=2863afd1, DeviceId=1004, FunctionCode=0
2025-05-26 11:26:12.327 +08:00 [WRN] 设备 1004 健康检查失败，连续失败次数: 1
2025-05-26 11:26:12.328 +08:00 [DBG] 完成请求监控: RequestId=2863afd1, Success=false, TotalTime=0.5688ms, ExecutionTime=0.5688ms
2025-05-26 11:26:12.328 +08:00 [WRN] 请求失败: RequestId=2863afd1, DeviceId=1004, Error=Health check failed
2025-05-26 11:26:12.329 +08:00 [DBG] 开始监控请求: RequestId=1924ff4b, DeviceId=1005, FunctionCode=0
2025-05-26 11:26:12.329 +08:00 [WRN] 设备 1005 健康检查失败，连续失败次数: 1
2025-05-26 11:26:12.329 +08:00 [DBG] 完成请求监控: RequestId=1924ff4b, Success=false, TotalTime=0.5614ms, ExecutionTime=0.5614ms
2025-05-26 11:26:12.329 +08:00 [WRN] 请求失败: RequestId=1924ff4b, DeviceId=1005, Error=Health check failed
2025-05-26 11:26:12.330 +08:00 [DBG] 开始监控请求: RequestId=51b4f019, DeviceId=1006, FunctionCode=0
2025-05-26 11:26:12.330 +08:00 [WRN] 设备 1006 健康检查失败，连续失败次数: 1
2025-05-26 11:26:12.330 +08:00 [DBG] 完成请求监控: RequestId=51b4f019, Success=false, TotalTime=0.5313ms, ExecutionTime=0.5313ms
2025-05-26 11:26:12.330 +08:00 [WRN] 请求失败: RequestId=51b4f019, DeviceId=1006, Error=Health check failed
2025-05-26 11:26:12.331 +08:00 [DBG] 开始监控请求: RequestId=31c3c812, DeviceId=1007, FunctionCode=0
2025-05-26 11:26:12.331 +08:00 [WRN] 设备 1007 健康检查失败，连续失败次数: 1
2025-05-26 11:26:12.332 +08:00 [DBG] 完成请求监控: RequestId=31c3c812, Success=false, TotalTime=0.5695ms, ExecutionTime=0.5695ms
2025-05-26 11:26:12.332 +08:00 [WRN] 请求失败: RequestId=31c3c812, DeviceId=1007, Error=Health check failed
2025-05-26 11:26:12.332 +08:00 [DBG] 开始监控请求: RequestId=273bc933, DeviceId=1008, FunctionCode=0
2025-05-26 11:26:12.332 +08:00 [WRN] 设备 1008 健康检查失败，连续失败次数: 1
2025-05-26 11:26:12.333 +08:00 [DBG] 完成请求监控: RequestId=273bc933, Success=false, TotalTime=0.8937ms, ExecutionTime=0.8937ms
2025-05-26 11:26:12.333 +08:00 [WRN] 请求失败: RequestId=273bc933, DeviceId=1008, Error=Health check failed
2025-05-26 11:26:12.334 +08:00 [DBG] 开始监控请求: RequestId=5a462911, DeviceId=1009, FunctionCode=0
2025-05-26 11:26:12.334 +08:00 [WRN] 设备 1009 健康检查失败，连续失败次数: 1
2025-05-26 11:26:12.334 +08:00 [DBG] 完成请求监控: RequestId=5a462911, Success=false, TotalTime=0.5892ms, ExecutionTime=0.5892ms
2025-05-26 11:26:12.334 +08:00 [WRN] 请求失败: RequestId=5a462911, DeviceId=1009, Error=Health check failed
2025-05-26 11:26:12.335 +08:00 [DBG] 开始监控请求: RequestId=33f255d5, DeviceId=1010, FunctionCode=0
2025-05-26 11:26:12.335 +08:00 [WRN] 设备 1010 健康检查失败，连续失败次数: 1
2025-05-26 11:26:12.336 +08:00 [DBG] 完成请求监控: RequestId=33f255d5, Success=false, TotalTime=0.5448ms, ExecutionTime=0.5448ms
2025-05-26 11:26:12.336 +08:00 [WRN] 请求失败: RequestId=33f255d5, DeviceId=1010, Error=Health check failed
2025-05-26 11:26:12.336 +08:00 [DBG] 开始监控请求: RequestId=072d3390, DeviceId=2001, FunctionCode=0
2025-05-26 11:26:12.337 +08:00 [DBG] 开始读取保持寄存器: SlaveId=255, StartAddress=0, Quantity=1
2025-05-26 11:26:12.364 +08:00 [INF] Now listening on: http://localhost:5000
2025-05-26 11:26:12.366 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-05-26 11:26:12.366 +08:00 [INF] Hosting environment: Production
2025-05-26 11:26:12.367 +08:00 [INF] Content root path: C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway
2025-05-26 11:26:17.543 +08:00 [DBG] 设备 2001 健康检查失败: The write timed out.
2025-05-26 11:26:17.543 +08:00 [WRN] 设备 2001 健康检查失败，连续失败次数: 1
2025-05-26 11:26:17.544 +08:00 [DBG] 完成请求监控: RequestId=072d3390, Success=false, TotalTime=5207.8673ms, ExecutionTime=5207.8673ms
2025-05-26 11:26:17.544 +08:00 [WRN] 请求失败: RequestId=072d3390, DeviceId=2001, Error=Health check failed
2025-05-26 11:26:41.697 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/ - null null
2025-05-26 11:26:41.713 +08:00 [INF] Executing endpoint 'HTTP: GET /'
2025-05-26 11:26:41.719 +08:00 [INF] Executed endpoint 'HTTP: GET /'
2025-05-26 11:26:41.722 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/ - 200 null text/plain; charset=utf-8 25.4891ms
2025-05-26 11:26:42.285 +08:00 [INF] === Modbus性能报告 ===
2025-05-26 11:26:42.288 +08:00 [INF] 设备 1001: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:26:42.290 +08:00 [WRN] 设备 1001 最近错误: Health check failed
2025-05-26 11:26:42.291 +08:00 [INF] 设备 1002: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:26:42.293 +08:00 [WRN] 设备 1002 最近错误: Health check failed
2025-05-26 11:26:42.293 +08:00 [INF] 设备 1003: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:26:42.295 +08:00 [WRN] 设备 1003 最近错误: Health check failed
2025-05-26 11:26:42.296 +08:00 [INF] 设备 1004: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:26:42.298 +08:00 [WRN] 设备 1004 最近错误: Health check failed
2025-05-26 11:26:42.299 +08:00 [INF] 设备 1005: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:26:42.300 +08:00 [WRN] 设备 1005 最近错误: Health check failed
2025-05-26 11:26:42.301 +08:00 [INF] 设备 1006: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:26:42.302 +08:00 [WRN] 设备 1006 最近错误: Health check failed
2025-05-26 11:26:42.303 +08:00 [INF] 设备 1007: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:26:42.304 +08:00 [WRN] 设备 1007 最近错误: Health check failed
2025-05-26 11:26:42.305 +08:00 [INF] 设备 1008: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:26:42.306 +08:00 [WRN] 设备 1008 最近错误: Health check failed
2025-05-26 11:26:42.307 +08:00 [INF] 设备 1009: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:26:42.309 +08:00 [WRN] 设备 1009 最近错误: Health check failed
2025-05-26 11:26:42.310 +08:00 [INF] 设备 1010: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:26:42.312 +08:00 [WRN] 设备 1010 最近错误: Health check failed
2025-05-26 11:26:42.314 +08:00 [INF] 设备 2001: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:26:42.316 +08:00 [WRN] 设备 2001 最近错误: Health check failed
2025-05-26 11:26:42.317 +08:00 [INF] === 性能报告结束 ===
2025-05-26 11:26:42.510 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/favicon.ico - null null
2025-05-26 11:26:42.513 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/favicon.ico - 404 0 null 4.2131ms
2025-05-26 11:26:42.517 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/favicon.ico, Response status code: 404
2025-05-26 11:26:47.548 +08:00 [DBG] 开始监控请求: RequestId=bcd51b51, DeviceId=1001, FunctionCode=0
2025-05-26 11:26:47.548 +08:00 [WRN] 设备 1001 健康检查失败，连续失败次数: 2
2025-05-26 11:26:47.550 +08:00 [DBG] 完成请求监控: RequestId=bcd51b51, Success=false, TotalTime=2.4558ms, ExecutionTime=2.4558ms
2025-05-26 11:26:47.550 +08:00 [WRN] 请求失败: RequestId=bcd51b51, DeviceId=1001, Error=Health check failed
2025-05-26 11:26:47.553 +08:00 [DBG] 开始监控请求: RequestId=7224331a, DeviceId=1002, FunctionCode=0
2025-05-26 11:26:47.553 +08:00 [WRN] 设备 1002 健康检查失败，连续失败次数: 2
2025-05-26 11:26:47.554 +08:00 [DBG] 完成请求监控: RequestId=7224331a, Success=false, TotalTime=1.4939ms, ExecutionTime=1.4939ms
2025-05-26 11:26:47.554 +08:00 [WRN] 请求失败: RequestId=7224331a, DeviceId=1002, Error=Health check failed
2025-05-26 11:26:47.556 +08:00 [DBG] 开始监控请求: RequestId=bc54a74b, DeviceId=1003, FunctionCode=0
2025-05-26 11:26:47.556 +08:00 [WRN] 设备 1003 健康检查失败，连续失败次数: 2
2025-05-26 11:26:47.557 +08:00 [DBG] 完成请求监控: RequestId=bc54a74b, Success=false, TotalTime=0.9887ms, ExecutionTime=0.9887ms
2025-05-26 11:26:47.557 +08:00 [WRN] 请求失败: RequestId=bc54a74b, DeviceId=1003, Error=Health check failed
2025-05-26 11:26:47.558 +08:00 [DBG] 开始监控请求: RequestId=16feb7ae, DeviceId=1004, FunctionCode=0
2025-05-26 11:26:47.558 +08:00 [WRN] 设备 1004 健康检查失败，连续失败次数: 2
2025-05-26 11:26:47.559 +08:00 [DBG] 完成请求监控: RequestId=16feb7ae, Success=false, TotalTime=0.9691ms, ExecutionTime=0.9691ms
2025-05-26 11:26:47.559 +08:00 [WRN] 请求失败: RequestId=16feb7ae, DeviceId=1004, Error=Health check failed
2025-05-26 11:26:47.560 +08:00 [DBG] 开始监控请求: RequestId=1dbf2697, DeviceId=1005, FunctionCode=0
2025-05-26 11:26:47.560 +08:00 [WRN] 设备 1005 健康检查失败，连续失败次数: 2
2025-05-26 11:26:47.560 +08:00 [DBG] 完成请求监控: RequestId=1dbf2697, Success=false, TotalTime=0.7549ms, ExecutionTime=0.7549ms
2025-05-26 11:26:47.560 +08:00 [WRN] 请求失败: RequestId=1dbf2697, DeviceId=1005, Error=Health check failed
2025-05-26 11:26:47.561 +08:00 [DBG] 开始监控请求: RequestId=d32c2bb2, DeviceId=1006, FunctionCode=0
2025-05-26 11:26:47.561 +08:00 [WRN] 设备 1006 健康检查失败，连续失败次数: 2
2025-05-26 11:26:47.562 +08:00 [DBG] 完成请求监控: RequestId=d32c2bb2, Success=false, TotalTime=0.5719ms, ExecutionTime=0.5719ms
2025-05-26 11:26:47.562 +08:00 [WRN] 请求失败: RequestId=d32c2bb2, DeviceId=1006, Error=Health check failed
2025-05-26 11:26:47.563 +08:00 [DBG] 开始监控请求: RequestId=8875f4f2, DeviceId=1007, FunctionCode=0
2025-05-26 11:26:47.563 +08:00 [WRN] 设备 1007 健康检查失败，连续失败次数: 2
2025-05-26 11:26:47.563 +08:00 [DBG] 完成请求监控: RequestId=8875f4f2, Success=false, TotalTime=0.5517ms, ExecutionTime=0.5517ms
2025-05-26 11:26:47.563 +08:00 [WRN] 请求失败: RequestId=8875f4f2, DeviceId=1007, Error=Health check failed
2025-05-26 11:26:47.565 +08:00 [DBG] 开始监控请求: RequestId=8f921628, DeviceId=1008, FunctionCode=0
2025-05-26 11:26:47.565 +08:00 [WRN] 设备 1008 健康检查失败，连续失败次数: 2
2025-05-26 11:26:47.565 +08:00 [DBG] 完成请求监控: RequestId=8f921628, Success=false, TotalTime=0.8337ms, ExecutionTime=0.8337ms
2025-05-26 11:26:47.565 +08:00 [WRN] 请求失败: RequestId=8f921628, DeviceId=1008, Error=Health check failed
2025-05-26 11:26:47.566 +08:00 [DBG] 开始监控请求: RequestId=035ecbd5, DeviceId=1009, FunctionCode=0
2025-05-26 11:26:47.566 +08:00 [WRN] 设备 1009 健康检查失败，连续失败次数: 2
2025-05-26 11:26:47.567 +08:00 [DBG] 完成请求监控: RequestId=035ecbd5, Success=false, TotalTime=0.6152ms, ExecutionTime=0.6152ms
2025-05-26 11:26:47.567 +08:00 [WRN] 请求失败: RequestId=035ecbd5, DeviceId=1009, Error=Health check failed
2025-05-26 11:26:47.568 +08:00 [DBG] 开始监控请求: RequestId=50cc9e4f, DeviceId=1010, FunctionCode=0
2025-05-26 11:26:47.568 +08:00 [WRN] 设备 1010 健康检查失败，连续失败次数: 2
2025-05-26 11:26:47.568 +08:00 [DBG] 完成请求监控: RequestId=50cc9e4f, Success=false, TotalTime=0.5655ms, ExecutionTime=0.5655ms
2025-05-26 11:26:47.568 +08:00 [WRN] 请求失败: RequestId=50cc9e4f, DeviceId=1010, Error=Health check failed
2025-05-26 11:26:47.569 +08:00 [DBG] 开始监控请求: RequestId=4ded6ee1, DeviceId=2001, FunctionCode=0
2025-05-26 11:26:47.569 +08:00 [DBG] 开始读取保持寄存器: SlaveId=255, StartAddress=0, Quantity=1
2025-05-26 11:26:52.794 +08:00 [DBG] 设备 2001 健康检查失败: The write timed out.
2025-05-26 11:26:52.794 +08:00 [WRN] 设备 2001 健康检查失败，连续失败次数: 2
2025-05-26 11:26:52.796 +08:00 [DBG] 完成请求监控: RequestId=4ded6ee1, Success=false, TotalTime=5227.5535ms, ExecutionTime=5227.5535ms
2025-05-26 11:26:52.796 +08:00 [WRN] 请求失败: RequestId=4ded6ee1, DeviceId=2001, Error=Health check failed
2025-05-26 11:27:03.349 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger - null null
2025-05-26 11:27:03.350 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger - 404 0 null 1.6678ms
2025-05-26 11:27:03.353 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/swagger, Response status code: 404
2025-05-26 11:27:11.143 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/ - null null
2025-05-26 11:27:11.145 +08:00 [INF] Executing endpoint 'HTTP: GET /'
2025-05-26 11:27:11.146 +08:00 [INF] Executed endpoint 'HTTP: GET /'
2025-05-26 11:27:11.147 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/ - 200 null text/plain; charset=utf-8 4.0443ms
2025-05-26 11:27:12.278 +08:00 [INF] === Modbus性能报告 ===
2025-05-26 11:27:12.279 +08:00 [INF] 设备 1001: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:27:12.281 +08:00 [WRN] 设备 1001 最近错误: Health check failed; Health check failed
2025-05-26 11:27:12.282 +08:00 [INF] 设备 1002: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:27:12.284 +08:00 [WRN] 设备 1002 最近错误: Health check failed; Health check failed
2025-05-26 11:27:12.286 +08:00 [INF] 设备 1003: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:27:12.287 +08:00 [WRN] 设备 1003 最近错误: Health check failed; Health check failed
2025-05-26 11:27:12.288 +08:00 [INF] 设备 1004: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:27:12.289 +08:00 [WRN] 设备 1004 最近错误: Health check failed; Health check failed
2025-05-26 11:27:12.290 +08:00 [INF] 设备 1005: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:27:12.291 +08:00 [WRN] 设备 1005 最近错误: Health check failed; Health check failed
2025-05-26 11:27:12.292 +08:00 [INF] 设备 1006: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:27:12.294 +08:00 [WRN] 设备 1006 最近错误: Health check failed; Health check failed
2025-05-26 11:27:12.295 +08:00 [INF] 设备 1007: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:27:12.296 +08:00 [WRN] 设备 1007 最近错误: Health check failed; Health check failed
2025-05-26 11:27:12.297 +08:00 [INF] 设备 1008: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:27:12.299 +08:00 [WRN] 设备 1008 最近错误: Health check failed; Health check failed
2025-05-26 11:27:12.299 +08:00 [INF] 设备 1009: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:27:12.300 +08:00 [WRN] 设备 1009 最近错误: Health check failed; Health check failed
2025-05-26 11:27:12.301 +08:00 [INF] 设备 1010: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:27:12.302 +08:00 [WRN] 设备 1010 最近错误: Health check failed; Health check failed
2025-05-26 11:27:12.303 +08:00 [INF] 设备 2001: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:27:12.305 +08:00 [WRN] 设备 2001 最近错误: Health check failed; Health check failed
2025-05-26 11:27:12.306 +08:00 [INF] === 性能报告结束 ===
2025-05-26 11:27:20.290 +08:00 [INF] Application is shutting down...
2025-05-26 11:27:20.294 +08:00 [INF] 连接健康检查服务正在停止
2025-05-26 11:27:20.296 +08:00 [INF] 正在停止Modbus TCP网关服务...
2025-05-26 11:27:20.299 +08:00 [INF] Modbus TCP网关服务已停止
2025-05-26 11:27:20.303 +08:00 [DBG] 正在释放线程安全ModbusClient连接池资源
2025-05-26 11:27:20.303 +08:00 [DBG] 线程安全ModbusClient连接池资源已释放
2025-05-26 11:27:20.303 +08:00 [INF] 正在输出最终性能报告...
2025-05-26 11:27:20.304 +08:00 [INF] === Modbus性能报告 ===
2025-05-26 11:27:20.304 +08:00 [INF] 设备 1001: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:27:20.305 +08:00 [WRN] 设备 1001 最近错误: Health check failed; Health check failed
2025-05-26 11:27:20.306 +08:00 [INF] 设备 1002: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:27:20.308 +08:00 [WRN] 设备 1002 最近错误: Health check failed; Health check failed
2025-05-26 11:27:20.309 +08:00 [INF] 设备 1003: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:27:20.311 +08:00 [WRN] 设备 1003 最近错误: Health check failed; Health check failed
2025-05-26 11:27:20.312 +08:00 [INF] 设备 1004: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:27:20.314 +08:00 [WRN] 设备 1004 最近错误: Health check failed; Health check failed
2025-05-26 11:27:20.315 +08:00 [INF] 设备 1005: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:27:20.317 +08:00 [WRN] 设备 1005 最近错误: Health check failed; Health check failed
2025-05-26 11:27:20.318 +08:00 [INF] 设备 1006: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:27:20.320 +08:00 [WRN] 设备 1006 最近错误: Health check failed; Health check failed
2025-05-26 11:27:20.321 +08:00 [INF] 设备 1007: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:27:20.323 +08:00 [WRN] 设备 1007 最近错误: Health check failed; Health check failed
2025-05-26 11:27:20.323 +08:00 [INF] 设备 1008: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:27:20.325 +08:00 [WRN] 设备 1008 最近错误: Health check failed; Health check failed
2025-05-26 11:27:20.325 +08:00 [INF] 设备 1009: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:27:20.327 +08:00 [WRN] 设备 1009 最近错误: Health check failed; Health check failed
2025-05-26 11:27:20.327 +08:00 [INF] 设备 1010: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:27:20.328 +08:00 [WRN] 设备 1010 最近错误: Health check failed; Health check failed
2025-05-26 11:27:20.329 +08:00 [INF] 设备 2001: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:27:20.330 +08:00 [WRN] 设备 2001 最近错误: Health check failed; Health check failed
2025-05-26 11:27:20.331 +08:00 [INF] === 性能报告结束 ===
2025-05-26 11:28:01.553 +08:00 [INF] 正在验证配置...
2025-05-26 11:28:01.580 +08:00 [INF] 配置验证通过
2025-05-26 11:28:01.627 +08:00 [INF] 正在启动Modbus TCP网关服务...
2025-05-26 11:28:01.631 +08:00 [INF] 创建Modbus客户端：ID=1001, IP=***************, Port=8080
2025-05-26 11:28:01.636 +08:00 [DBG] 已注册设备 1001 的线程安全客户端包装器
2025-05-26 11:28:01.636 +08:00 [INF] 已注册设备 1001 到线程安全连接池
2025-05-26 11:28:01.636 +08:00 [INF] 创建Modbus客户端：ID=1002, IP=***************, Port=8080
2025-05-26 11:28:01.637 +08:00 [DBG] 已注册设备 1002 的线程安全客户端包装器
2025-05-26 11:28:01.637 +08:00 [INF] 已注册设备 1002 到线程安全连接池
2025-05-26 11:28:01.638 +08:00 [INF] 创建Modbus客户端：ID=1003, IP=***************, Port=8080
2025-05-26 11:28:01.638 +08:00 [DBG] 已注册设备 1003 的线程安全客户端包装器
2025-05-26 11:28:01.638 +08:00 [INF] 已注册设备 1003 到线程安全连接池
2025-05-26 11:28:01.639 +08:00 [INF] 创建Modbus客户端：ID=1004, IP=***************, Port=8080
2025-05-26 11:28:01.639 +08:00 [DBG] 已注册设备 1004 的线程安全客户端包装器
2025-05-26 11:28:01.639 +08:00 [INF] 已注册设备 1004 到线程安全连接池
2025-05-26 11:28:01.640 +08:00 [INF] 创建Modbus客户端：ID=1005, IP=***************, Port=8080
2025-05-26 11:28:01.640 +08:00 [DBG] 已注册设备 1005 的线程安全客户端包装器
2025-05-26 11:28:01.640 +08:00 [INF] 已注册设备 1005 到线程安全连接池
2025-05-26 11:28:01.641 +08:00 [INF] 创建Modbus客户端：ID=1006, IP=***************, Port=8080
2025-05-26 11:28:01.642 +08:00 [DBG] 已注册设备 1006 的线程安全客户端包装器
2025-05-26 11:28:01.642 +08:00 [INF] 已注册设备 1006 到线程安全连接池
2025-05-26 11:28:01.642 +08:00 [INF] 创建Modbus客户端：ID=1007, IP=***************, Port=8080
2025-05-26 11:28:01.643 +08:00 [DBG] 已注册设备 1007 的线程安全客户端包装器
2025-05-26 11:28:01.643 +08:00 [INF] 已注册设备 1007 到线程安全连接池
2025-05-26 11:28:01.643 +08:00 [INF] 创建Modbus客户端：ID=1008, IP=***************, Port=8080
2025-05-26 11:28:01.644 +08:00 [DBG] 已注册设备 1008 的线程安全客户端包装器
2025-05-26 11:28:01.644 +08:00 [INF] 已注册设备 1008 到线程安全连接池
2025-05-26 11:28:01.645 +08:00 [INF] 创建Modbus客户端：ID=1009, IP=***************, Port=8080
2025-05-26 11:28:01.646 +08:00 [DBG] 已注册设备 1009 的线程安全客户端包装器
2025-05-26 11:28:01.646 +08:00 [INF] 已注册设备 1009 到线程安全连接池
2025-05-26 11:28:01.646 +08:00 [INF] 创建Modbus客户端：ID=1010, IP=***************, Port=8080
2025-05-26 11:28:01.647 +08:00 [DBG] 已注册设备 1010 的线程安全客户端包装器
2025-05-26 11:28:01.647 +08:00 [INF] 已注册设备 1010 到线程安全连接池
2025-05-26 11:28:01.647 +08:00 [INF] 创建Modbus客户端并连接到设备：ID=2001, PortName=COM3, BaudRate=9600
2025-05-26 11:28:01.653 +08:00 [DBG] 已注册设备 2001 的线程安全客户端包装器
2025-05-26 11:28:01.653 +08:00 [INF] 已注册设备 2001 到线程安全连接池
2025-05-26 11:28:01.656 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8080
2025-05-26 11:28:01.657 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8081
2025-05-26 11:28:01.658 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8082
2025-05-26 11:28:01.658 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8083
2025-05-26 11:28:01.659 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8084
2025-05-26 11:28:01.659 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8085
2025-05-26 11:28:01.660 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8086
2025-05-26 11:28:01.660 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8087
2025-05-26 11:28:01.661 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8088
2025-05-26 11:28:01.661 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8089
2025-05-26 11:28:01.663 +08:00 [INF] Modbus连接健康检查服务已启动
2025-05-26 11:28:01.665 +08:00 [DBG] 开始监控请求: RequestId=6e82f1a8, DeviceId=1001, FunctionCode=0
2025-05-26 11:28:01.666 +08:00 [WRN] 设备 1001 健康检查失败，连续失败次数: 1
2025-05-26 11:28:01.667 +08:00 [DBG] 完成请求监控: RequestId=6e82f1a8, Success=false, TotalTime=2.0449ms, ExecutionTime=2.0449ms
2025-05-26 11:28:01.668 +08:00 [WRN] 请求失败: RequestId=6e82f1a8, DeviceId=1001, Error=Health check failed
2025-05-26 11:28:01.669 +08:00 [DBG] 开始监控请求: RequestId=7c74a1e6, DeviceId=1002, FunctionCode=0
2025-05-26 11:28:01.669 +08:00 [WRN] 设备 1002 健康检查失败，连续失败次数: 1
2025-05-26 11:28:01.669 +08:00 [DBG] 完成请求监控: RequestId=7c74a1e6, Success=false, TotalTime=0.5297ms, ExecutionTime=0.5297ms
2025-05-26 11:28:01.669 +08:00 [WRN] 请求失败: RequestId=7c74a1e6, DeviceId=1002, Error=Health check failed
2025-05-26 11:28:01.670 +08:00 [DBG] 开始监控请求: RequestId=812d7be4, DeviceId=1003, FunctionCode=0
2025-05-26 11:28:01.670 +08:00 [WRN] 设备 1003 健康检查失败，连续失败次数: 1
2025-05-26 11:28:01.671 +08:00 [DBG] 完成请求监控: RequestId=812d7be4, Success=false, TotalTime=0.8673ms, ExecutionTime=0.8673ms
2025-05-26 11:28:01.671 +08:00 [WRN] 请求失败: RequestId=812d7be4, DeviceId=1003, Error=Health check failed
2025-05-26 11:28:01.672 +08:00 [DBG] 开始监控请求: RequestId=2d45a3b8, DeviceId=1004, FunctionCode=0
2025-05-26 11:28:01.672 +08:00 [WRN] 设备 1004 健康检查失败，连续失败次数: 1
2025-05-26 11:28:01.672 +08:00 [DBG] 完成请求监控: RequestId=2d45a3b8, Success=false, TotalTime=0.6889ms, ExecutionTime=0.6889ms
2025-05-26 11:28:01.672 +08:00 [WRN] 请求失败: RequestId=2d45a3b8, DeviceId=1004, Error=Health check failed
2025-05-26 11:28:01.673 +08:00 [DBG] 开始监控请求: RequestId=f028e92e, DeviceId=1005, FunctionCode=0
2025-05-26 11:28:01.673 +08:00 [WRN] 设备 1005 健康检查失败，连续失败次数: 1
2025-05-26 11:28:01.674 +08:00 [DBG] 完成请求监控: RequestId=f028e92e, Success=false, TotalTime=0.4986ms, ExecutionTime=0.4986ms
2025-05-26 11:28:01.674 +08:00 [WRN] 请求失败: RequestId=f028e92e, DeviceId=1005, Error=Health check failed
2025-05-26 11:28:01.674 +08:00 [DBG] 开始监控请求: RequestId=66f817fb, DeviceId=1006, FunctionCode=0
2025-05-26 11:28:01.674 +08:00 [WRN] 设备 1006 健康检查失败，连续失败次数: 1
2025-05-26 11:28:01.675 +08:00 [DBG] 完成请求监控: RequestId=66f817fb, Success=false, TotalTime=0.5708ms, ExecutionTime=0.5708ms
2025-05-26 11:28:01.675 +08:00 [WRN] 请求失败: RequestId=66f817fb, DeviceId=1006, Error=Health check failed
2025-05-26 11:28:01.675 +08:00 [DBG] 开始监控请求: RequestId=50fc6e7c, DeviceId=1007, FunctionCode=0
2025-05-26 11:28:01.675 +08:00 [WRN] 设备 1007 健康检查失败，连续失败次数: 1
2025-05-26 11:28:01.676 +08:00 [DBG] 完成请求监控: RequestId=50fc6e7c, Success=false, TotalTime=0.5401ms, ExecutionTime=0.5401ms
2025-05-26 11:28:01.676 +08:00 [WRN] 请求失败: RequestId=50fc6e7c, DeviceId=1007, Error=Health check failed
2025-05-26 11:28:01.676 +08:00 [DBG] 开始监控请求: RequestId=d106d558, DeviceId=1008, FunctionCode=0
2025-05-26 11:28:01.677 +08:00 [WRN] 设备 1008 健康检查失败，连续失败次数: 1
2025-05-26 11:28:01.677 +08:00 [DBG] 完成请求监控: RequestId=d106d558, Success=false, TotalTime=0.493ms, ExecutionTime=0.493ms
2025-05-26 11:28:01.677 +08:00 [WRN] 请求失败: RequestId=d106d558, DeviceId=1008, Error=Health check failed
2025-05-26 11:28:01.678 +08:00 [DBG] 开始监控请求: RequestId=e5c7ff90, DeviceId=1009, FunctionCode=0
2025-05-26 11:28:01.678 +08:00 [WRN] 设备 1009 健康检查失败，连续失败次数: 1
2025-05-26 11:28:01.678 +08:00 [DBG] 完成请求监控: RequestId=e5c7ff90, Success=false, TotalTime=0.5037ms, ExecutionTime=0.5037ms
2025-05-26 11:28:01.678 +08:00 [WRN] 请求失败: RequestId=e5c7ff90, DeviceId=1009, Error=Health check failed
2025-05-26 11:28:01.679 +08:00 [DBG] 开始监控请求: RequestId=5db5f5f1, DeviceId=1010, FunctionCode=0
2025-05-26 11:28:01.679 +08:00 [WRN] 设备 1010 健康检查失败，连续失败次数: 1
2025-05-26 11:28:01.680 +08:00 [DBG] 完成请求监控: RequestId=5db5f5f1, Success=false, TotalTime=0.76ms, ExecutionTime=0.76ms
2025-05-26 11:28:01.680 +08:00 [WRN] 请求失败: RequestId=5db5f5f1, DeviceId=1010, Error=Health check failed
2025-05-26 11:28:01.680 +08:00 [DBG] 开始监控请求: RequestId=497393f5, DeviceId=2001, FunctionCode=0
2025-05-26 11:28:01.681 +08:00 [DBG] 开始读取保持寄存器: SlaveId=255, StartAddress=0, Quantity=1
2025-05-26 11:28:01.705 +08:00 [INF] Now listening on: http://localhost:5000
2025-05-26 11:28:01.705 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-05-26 11:28:01.706 +08:00 [INF] Hosting environment: Production
2025-05-26 11:28:01.706 +08:00 [INF] Content root path: C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway
2025-05-26 11:28:06.909 +08:00 [DBG] 设备 2001 健康检查失败: The write timed out.
2025-05-26 11:28:06.909 +08:00 [WRN] 设备 2001 健康检查失败，连续失败次数: 1
2025-05-26 11:28:06.910 +08:00 [DBG] 完成请求监控: RequestId=497393f5, Success=false, TotalTime=5229.906ms, ExecutionTime=5229.906ms
2025-05-26 11:28:06.910 +08:00 [WRN] 请求失败: RequestId=497393f5, DeviceId=2001, Error=Health check failed
2025-05-26 11:28:31.640 +08:00 [INF] === Modbus性能报告 ===
2025-05-26 11:28:31.645 +08:00 [INF] 设备 1001: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:28:31.647 +08:00 [WRN] 设备 1001 最近错误: Health check failed
2025-05-26 11:28:31.648 +08:00 [INF] 设备 1002: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:28:31.650 +08:00 [WRN] 设备 1002 最近错误: Health check failed
2025-05-26 11:28:31.651 +08:00 [INF] 设备 1003: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:28:31.652 +08:00 [WRN] 设备 1003 最近错误: Health check failed
2025-05-26 11:28:31.653 +08:00 [INF] 设备 1004: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:28:31.654 +08:00 [WRN] 设备 1004 最近错误: Health check failed
2025-05-26 11:28:31.655 +08:00 [INF] 设备 1005: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:28:31.656 +08:00 [WRN] 设备 1005 最近错误: Health check failed
2025-05-26 11:28:31.657 +08:00 [INF] 设备 1006: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:28:31.658 +08:00 [WRN] 设备 1006 最近错误: Health check failed
2025-05-26 11:28:31.659 +08:00 [INF] 设备 1007: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:28:31.660 +08:00 [WRN] 设备 1007 最近错误: Health check failed
2025-05-26 11:28:31.661 +08:00 [INF] 设备 1008: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:28:31.662 +08:00 [WRN] 设备 1008 最近错误: Health check failed
2025-05-26 11:28:31.663 +08:00 [INF] 设备 1009: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:28:31.664 +08:00 [WRN] 设备 1009 最近错误: Health check failed
2025-05-26 11:28:31.665 +08:00 [INF] 设备 1010: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:28:31.666 +08:00 [WRN] 设备 1010 最近错误: Health check failed
2025-05-26 11:28:31.667 +08:00 [INF] 设备 2001: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:28:31.668 +08:00 [WRN] 设备 2001 最近错误: Health check failed
2025-05-26 11:28:31.669 +08:00 [INF] === 性能报告结束 ===
2025-05-26 11:28:36.915 +08:00 [DBG] 开始监控请求: RequestId=4ef63336, DeviceId=1001, FunctionCode=0
2025-05-26 11:28:36.915 +08:00 [WRN] 设备 1001 健康检查失败，连续失败次数: 2
2025-05-26 11:28:36.918 +08:00 [DBG] 完成请求监控: RequestId=4ef63336, Success=false, TotalTime=3.0555ms, ExecutionTime=3.0555ms
2025-05-26 11:28:36.918 +08:00 [WRN] 请求失败: RequestId=4ef63336, DeviceId=1001, Error=Health check failed
2025-05-26 11:28:36.920 +08:00 [DBG] 开始监控请求: RequestId=0c4f0c0f, DeviceId=1002, FunctionCode=0
2025-05-26 11:28:36.920 +08:00 [WRN] 设备 1002 健康检查失败，连续失败次数: 2
2025-05-26 11:28:36.921 +08:00 [DBG] 完成请求监控: RequestId=0c4f0c0f, Success=false, TotalTime=1.4107ms, ExecutionTime=1.4107ms
2025-05-26 11:28:36.921 +08:00 [WRN] 请求失败: RequestId=0c4f0c0f, DeviceId=1002, Error=Health check failed
2025-05-26 11:28:36.923 +08:00 [DBG] 开始监控请求: RequestId=5fcaa7fb, DeviceId=1003, FunctionCode=0
2025-05-26 11:28:36.923 +08:00 [WRN] 设备 1003 健康检查失败，连续失败次数: 2
2025-05-26 11:28:36.924 +08:00 [DBG] 完成请求监控: RequestId=5fcaa7fb, Success=false, TotalTime=1.0558ms, ExecutionTime=1.0558ms
2025-05-26 11:28:36.924 +08:00 [WRN] 请求失败: RequestId=5fcaa7fb, DeviceId=1003, Error=Health check failed
2025-05-26 11:28:36.925 +08:00 [DBG] 开始监控请求: RequestId=57e2ce18, DeviceId=1004, FunctionCode=0
2025-05-26 11:28:36.925 +08:00 [WRN] 设备 1004 健康检查失败，连续失败次数: 2
2025-05-26 11:28:36.926 +08:00 [DBG] 完成请求监控: RequestId=57e2ce18, Success=false, TotalTime=0.7376ms, ExecutionTime=0.7376ms
2025-05-26 11:28:36.926 +08:00 [WRN] 请求失败: RequestId=57e2ce18, DeviceId=1004, Error=Health check failed
2025-05-26 11:28:36.927 +08:00 [DBG] 开始监控请求: RequestId=cf6ca3ed, DeviceId=1005, FunctionCode=0
2025-05-26 11:28:36.927 +08:00 [WRN] 设备 1005 健康检查失败，连续失败次数: 2
2025-05-26 11:28:36.927 +08:00 [DBG] 完成请求监控: RequestId=cf6ca3ed, Success=false, TotalTime=0.6762ms, ExecutionTime=0.6762ms
2025-05-26 11:28:36.927 +08:00 [WRN] 请求失败: RequestId=cf6ca3ed, DeviceId=1005, Error=Health check failed
2025-05-26 11:28:36.928 +08:00 [DBG] 开始监控请求: RequestId=f1966504, DeviceId=1006, FunctionCode=0
2025-05-26 11:28:36.928 +08:00 [WRN] 设备 1006 健康检查失败，连续失败次数: 2
2025-05-26 11:28:36.928 +08:00 [DBG] 完成请求监控: RequestId=f1966504, Success=false, TotalTime=0.498ms, ExecutionTime=0.498ms
2025-05-26 11:28:36.928 +08:00 [WRN] 请求失败: RequestId=f1966504, DeviceId=1006, Error=Health check failed
2025-05-26 11:28:36.929 +08:00 [DBG] 开始监控请求: RequestId=d88cdba9, DeviceId=1007, FunctionCode=0
2025-05-26 11:28:36.929 +08:00 [WRN] 设备 1007 健康检查失败，连续失败次数: 2
2025-05-26 11:28:36.930 +08:00 [DBG] 完成请求监控: RequestId=d88cdba9, Success=false, TotalTime=0.6753ms, ExecutionTime=0.6753ms
2025-05-26 11:28:36.930 +08:00 [WRN] 请求失败: RequestId=d88cdba9, DeviceId=1007, Error=Health check failed
2025-05-26 11:28:36.930 +08:00 [DBG] 开始监控请求: RequestId=d3901702, DeviceId=1008, FunctionCode=0
2025-05-26 11:28:36.930 +08:00 [WRN] 设备 1008 健康检查失败，连续失败次数: 2
2025-05-26 11:28:36.931 +08:00 [DBG] 完成请求监控: RequestId=d3901702, Success=false, TotalTime=0.5493ms, ExecutionTime=0.5493ms
2025-05-26 11:28:36.931 +08:00 [WRN] 请求失败: RequestId=d3901702, DeviceId=1008, Error=Health check failed
2025-05-26 11:28:36.932 +08:00 [DBG] 开始监控请求: RequestId=77ed7125, DeviceId=1009, FunctionCode=0
2025-05-26 11:28:36.932 +08:00 [WRN] 设备 1009 健康检查失败，连续失败次数: 2
2025-05-26 11:28:36.932 +08:00 [DBG] 完成请求监控: RequestId=77ed7125, Success=false, TotalTime=0.546ms, ExecutionTime=0.546ms
2025-05-26 11:28:36.932 +08:00 [WRN] 请求失败: RequestId=77ed7125, DeviceId=1009, Error=Health check failed
2025-05-26 11:28:36.933 +08:00 [DBG] 开始监控请求: RequestId=94072df1, DeviceId=1010, FunctionCode=0
2025-05-26 11:28:36.933 +08:00 [WRN] 设备 1010 健康检查失败，连续失败次数: 2
2025-05-26 11:28:36.933 +08:00 [DBG] 完成请求监控: RequestId=94072df1, Success=false, TotalTime=0.5341ms, ExecutionTime=0.5341ms
2025-05-26 11:28:36.933 +08:00 [WRN] 请求失败: RequestId=94072df1, DeviceId=1010, Error=Health check failed
2025-05-26 11:28:36.934 +08:00 [DBG] 开始监控请求: RequestId=68774a45, DeviceId=2001, FunctionCode=0
2025-05-26 11:28:36.934 +08:00 [DBG] 开始读取保持寄存器: SlaveId=255, StartAddress=0, Quantity=1
2025-05-26 11:28:42.171 +08:00 [DBG] 设备 2001 健康检查失败: The write timed out.
2025-05-26 11:28:42.171 +08:00 [WRN] 设备 2001 健康检查失败，连续失败次数: 2
2025-05-26 11:28:42.174 +08:00 [DBG] 完成请求监控: RequestId=68774a45, Success=false, TotalTime=5240.312ms, ExecutionTime=5240.312ms
2025-05-26 11:28:42.174 +08:00 [WRN] 请求失败: RequestId=68774a45, DeviceId=2001, Error=Health check failed
2025-05-26 11:28:43.016 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/ - null null
2025-05-26 11:28:43.028 +08:00 [INF] Executing endpoint 'HTTP: GET /'
2025-05-26 11:28:43.030 +08:00 [INF] Executed endpoint 'HTTP: GET /'
2025-05-26 11:28:43.032 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/ - 200 null text/plain; charset=utf-8 16.601ms
2025-05-26 11:28:52.319 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/monitoring/overview - null null
2025-05-26 11:28:52.321 +08:00 [INF] Executing endpoint 'Gsdt.ModbusGateway.Controllers.MonitoringController.GetSystemOverview (Gsdt.ModbusGateway)'
2025-05-26 11:28:52.335 +08:00 [INF] Route matched with {action = "GetSystemOverview", controller = "Monitoring"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[System.Object] GetSystemOverview() on controller Gsdt.ModbusGateway.Controllers.MonitoringController (Gsdt.ModbusGateway).
2025-05-26 11:28:52.342 +08:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType4`9[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int64, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Double, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Double, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-05-26 11:28:52.349 +08:00 [INF] Executed action Gsdt.ModbusGateway.Controllers.MonitoringController.GetSystemOverview (Gsdt.ModbusGateway) in 10.9277ms
2025-05-26 11:28:52.351 +08:00 [INF] Executed endpoint 'Gsdt.ModbusGateway.Controllers.MonitoringController.GetSystemOverview (Gsdt.ModbusGateway)'
2025-05-26 11:28:52.354 +08:00 [ERR] Connection id "0HNCS0LL62IM4", Request id "0HNCS0LL62IM4:00000001": An unhandled exception was thrown by the application.
System.NotSupportedException: JsonTypeInfo metadata for type '<>f__AnonymousType4`9[System.Int32,System.Int32,System.Int32,System.Int64,System.Int64,System.Int64,System.Double,System.Double,System.DateTime]' was not provided by TypeInfoResolver of type '[]'. If using source generation, ensure that all root types passed to the serializer have been annotated with 'JsonSerializableAttribute', along with any types that might be serialized polymorphically.
   at System.Text.Json.ThrowHelper.ThrowNotSupportedException_NoMetadataForType(Type type, IJsonTypeInfoResolver resolver)
   at System.Text.Json.JsonSerializerOptions.GetTypeInfoInternal(Type type, Boolean ensureConfigured, Nullable`1 ensureNotNull, Boolean resolveIfMutable, Boolean fallBackToNearestAncestorType)
   at System.Text.Json.JsonSerializerOptions.GetTypeInfo(Type type)
   at Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter.WriteResponseBodyAsync(OutputFormatterWriteContext context, Encoding selectedEncoding)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http.HttpProtocol.ProcessRequests[TContext](IHttpApplication`1 application)
2025-05-26 11:28:52.362 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/monitoring/overview - 500 0 null 43.8337ms
2025-05-26 11:29:01.621 +08:00 [INF] === Modbus性能报告 ===
2025-05-26 11:29:01.623 +08:00 [INF] 设备 1001: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:29:01.625 +08:00 [WRN] 设备 1001 最近错误: Health check failed; Health check failed
2025-05-26 11:29:01.625 +08:00 [INF] 设备 1002: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:29:01.627 +08:00 [WRN] 设备 1002 最近错误: Health check failed; Health check failed
2025-05-26 11:29:01.628 +08:00 [INF] 设备 1003: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:29:01.629 +08:00 [WRN] 设备 1003 最近错误: Health check failed; Health check failed
2025-05-26 11:29:01.629 +08:00 [INF] 设备 1004: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:29:01.630 +08:00 [WRN] 设备 1004 最近错误: Health check failed; Health check failed
2025-05-26 11:29:01.631 +08:00 [INF] 设备 1005: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:29:01.632 +08:00 [WRN] 设备 1005 最近错误: Health check failed; Health check failed
2025-05-26 11:29:01.633 +08:00 [INF] 设备 1006: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:29:01.634 +08:00 [WRN] 设备 1006 最近错误: Health check failed; Health check failed
2025-05-26 11:29:01.634 +08:00 [INF] 设备 1007: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:29:01.636 +08:00 [WRN] 设备 1007 最近错误: Health check failed; Health check failed
2025-05-26 11:29:01.636 +08:00 [INF] 设备 1008: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:29:01.638 +08:00 [WRN] 设备 1008 最近错误: Health check failed; Health check failed
2025-05-26 11:29:01.639 +08:00 [INF] 设备 1009: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:29:01.640 +08:00 [WRN] 设备 1009 最近错误: Health check failed; Health check failed
2025-05-26 11:29:01.641 +08:00 [INF] 设备 1010: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:29:01.642 +08:00 [WRN] 设备 1010 最近错误: Health check failed; Health check failed
2025-05-26 11:29:01.643 +08:00 [INF] 设备 2001: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:29:01.644 +08:00 [WRN] 设备 2001 最近错误: Health check failed; Health check failed
2025-05-26 11:29:01.644 +08:00 [INF] === 性能报告结束 ===
2025-05-26 11:29:12.186 +08:00 [DBG] 开始监控请求: RequestId=e0721e28, DeviceId=1001, FunctionCode=0
2025-05-26 11:29:12.186 +08:00 [WRN] 设备 1001 健康检查失败，连续失败次数: 3
2025-05-26 11:29:12.186 +08:00 [DBG] 完成请求监控: RequestId=e0721e28, Success=false, TotalTime=0.7346ms, ExecutionTime=0.7346ms
2025-05-26 11:29:12.186 +08:00 [WRN] 请求失败: RequestId=e0721e28, DeviceId=1001, Error=Health check failed
2025-05-26 11:29:12.188 +08:00 [INF] 尝试重连设备 1001
2025-05-26 11:29:12.188 +08:00 [DBG] 开始监控请求: RequestId=c541dd0c, DeviceId=1002, FunctionCode=0
2025-05-26 11:29:12.188 +08:00 [WRN] 设备 1002 健康检查失败，连续失败次数: 3
2025-05-26 11:29:12.189 +08:00 [DBG] 完成请求监控: RequestId=c541dd0c, Success=false, TotalTime=0.574ms, ExecutionTime=0.574ms
2025-05-26 11:29:12.189 +08:00 [WRN] 请求失败: RequestId=c541dd0c, DeviceId=1002, Error=Health check failed
2025-05-26 11:29:12.190 +08:00 [INF] 尝试重连设备 1002
2025-05-26 11:29:12.190 +08:00 [DBG] 开始监控请求: RequestId=fc6590f4, DeviceId=1003, FunctionCode=0
2025-05-26 11:29:12.190 +08:00 [WRN] 设备 1003 健康检查失败，连续失败次数: 3
2025-05-26 11:29:12.191 +08:00 [DBG] 完成请求监控: RequestId=fc6590f4, Success=false, TotalTime=0.6824ms, ExecutionTime=0.6824ms
2025-05-26 11:29:12.191 +08:00 [WRN] 请求失败: RequestId=fc6590f4, DeviceId=1003, Error=Health check failed
2025-05-26 11:29:12.192 +08:00 [INF] 尝试重连设备 1003
2025-05-26 11:29:12.192 +08:00 [DBG] 开始监控请求: RequestId=60f0559f, DeviceId=1004, FunctionCode=0
2025-05-26 11:29:12.192 +08:00 [WRN] 设备 1004 健康检查失败，连续失败次数: 3
2025-05-26 11:29:12.193 +08:00 [DBG] 完成请求监控: RequestId=60f0559f, Success=false, TotalTime=0.4991ms, ExecutionTime=0.4991ms
2025-05-26 11:29:12.193 +08:00 [WRN] 请求失败: RequestId=60f0559f, DeviceId=1004, Error=Health check failed
2025-05-26 11:29:12.193 +08:00 [INF] 尝试重连设备 1004
2025-05-26 11:29:12.194 +08:00 [DBG] 开始监控请求: RequestId=8cdc56c9, DeviceId=1005, FunctionCode=0
2025-05-26 11:29:12.194 +08:00 [WRN] 设备 1005 健康检查失败，连续失败次数: 3
2025-05-26 11:29:12.194 +08:00 [DBG] 完成请求监控: RequestId=8cdc56c9, Success=false, TotalTime=0.4964ms, ExecutionTime=0.4964ms
2025-05-26 11:29:12.194 +08:00 [WRN] 请求失败: RequestId=8cdc56c9, DeviceId=1005, Error=Health check failed
2025-05-26 11:29:12.195 +08:00 [INF] 尝试重连设备 1005
2025-05-26 11:29:12.195 +08:00 [DBG] 开始监控请求: RequestId=847afbb2, DeviceId=1006, FunctionCode=0
2025-05-26 11:29:12.195 +08:00 [WRN] 设备 1006 健康检查失败，连续失败次数: 3
2025-05-26 11:29:12.196 +08:00 [DBG] 完成请求监控: RequestId=847afbb2, Success=false, TotalTime=0.4995ms, ExecutionTime=0.4995ms
2025-05-26 11:29:12.196 +08:00 [WRN] 请求失败: RequestId=847afbb2, DeviceId=1006, Error=Health check failed
2025-05-26 11:29:12.196 +08:00 [INF] 尝试重连设备 1006
2025-05-26 11:29:12.197 +08:00 [DBG] 开始监控请求: RequestId=b18ea11f, DeviceId=1007, FunctionCode=0
2025-05-26 11:29:12.197 +08:00 [WRN] 设备 1007 健康检查失败，连续失败次数: 3
2025-05-26 11:29:12.197 +08:00 [DBG] 完成请求监控: RequestId=b18ea11f, Success=false, TotalTime=0.5216ms, ExecutionTime=0.5216ms
2025-05-26 11:29:12.197 +08:00 [WRN] 请求失败: RequestId=b18ea11f, DeviceId=1007, Error=Health check failed
2025-05-26 11:29:12.198 +08:00 [INF] 尝试重连设备 1007
2025-05-26 11:29:12.198 +08:00 [DBG] 开始监控请求: RequestId=a5e7ed80, DeviceId=1008, FunctionCode=0
2025-05-26 11:29:12.198 +08:00 [WRN] 设备 1008 健康检查失败，连续失败次数: 3
2025-05-26 11:29:12.199 +08:00 [DBG] 完成请求监控: RequestId=a5e7ed80, Success=false, TotalTime=0.5447ms, ExecutionTime=0.5447ms
2025-05-26 11:29:12.199 +08:00 [WRN] 请求失败: RequestId=a5e7ed80, DeviceId=1008, Error=Health check failed
2025-05-26 11:29:12.199 +08:00 [INF] 尝试重连设备 1008
2025-05-26 11:29:12.200 +08:00 [DBG] 开始监控请求: RequestId=979d4417, DeviceId=1009, FunctionCode=0
2025-05-26 11:29:12.200 +08:00 [WRN] 设备 1009 健康检查失败，连续失败次数: 3
2025-05-26 11:29:12.201 +08:00 [DBG] 完成请求监控: RequestId=979d4417, Success=false, TotalTime=0.7443ms, ExecutionTime=0.7443ms
2025-05-26 11:29:12.201 +08:00 [WRN] 请求失败: RequestId=979d4417, DeviceId=1009, Error=Health check failed
2025-05-26 11:29:12.201 +08:00 [INF] 尝试重连设备 1009
2025-05-26 11:29:12.202 +08:00 [DBG] 开始监控请求: RequestId=601427de, DeviceId=1010, FunctionCode=0
2025-05-26 11:29:12.202 +08:00 [WRN] 设备 1010 健康检查失败，连续失败次数: 3
2025-05-26 11:29:12.202 +08:00 [DBG] 完成请求监控: RequestId=601427de, Success=false, TotalTime=0.5541ms, ExecutionTime=0.5541ms
2025-05-26 11:29:12.202 +08:00 [WRN] 请求失败: RequestId=601427de, DeviceId=1010, Error=Health check failed
2025-05-26 11:29:12.203 +08:00 [INF] 尝试重连设备 1010
2025-05-26 11:29:12.204 +08:00 [DBG] 开始监控请求: RequestId=3777e9d2, DeviceId=2001, FunctionCode=0
2025-05-26 11:29:12.204 +08:00 [DBG] 开始读取保持寄存器: SlaveId=255, StartAddress=0, Quantity=1
2025-05-26 11:29:17.419 +08:00 [DBG] 设备 2001 健康检查失败: The write timed out.
2025-05-26 11:29:17.420 +08:00 [WRN] 设备 2001 健康检查失败，连续失败次数: 3
2025-05-26 11:29:17.422 +08:00 [DBG] 完成请求监控: RequestId=3777e9d2, Success=false, TotalTime=5218.7109ms, ExecutionTime=5218.7109ms
2025-05-26 11:29:17.422 +08:00 [WRN] 请求失败: RequestId=3777e9d2, DeviceId=2001, Error=Health check failed
2025-05-26 11:29:17.424 +08:00 [INF] 尝试重连设备 2001
2025-05-26 11:29:18.442 +08:00 [INF] 成功重连RTU设备 2001 (COM3)
2025-05-26 11:29:31.627 +08:00 [INF] === Modbus性能报告 ===
2025-05-26 11:29:31.627 +08:00 [INF] 设备 1001: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:29:31.629 +08:00 [WRN] 设备 1001 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:29:31.629 +08:00 [INF] 设备 1002: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:29:31.630 +08:00 [WRN] 设备 1002 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:29:31.631 +08:00 [INF] 设备 1003: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:29:31.632 +08:00 [WRN] 设备 1003 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:29:31.633 +08:00 [INF] 设备 1004: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:29:31.634 +08:00 [WRN] 设备 1004 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:29:31.634 +08:00 [INF] 设备 1005: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:29:31.636 +08:00 [WRN] 设备 1005 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:29:31.636 +08:00 [INF] 设备 1006: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:29:31.638 +08:00 [WRN] 设备 1006 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:29:31.640 +08:00 [INF] 设备 1007: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:29:31.641 +08:00 [WRN] 设备 1007 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:29:31.641 +08:00 [INF] 设备 1008: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:29:31.643 +08:00 [WRN] 设备 1008 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:29:31.643 +08:00 [INF] 设备 1009: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:29:31.645 +08:00 [WRN] 设备 1009 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:29:31.645 +08:00 [INF] 设备 1010: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:29:31.646 +08:00 [WRN] 设备 1010 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:29:31.647 +08:00 [INF] 设备 2001: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:29:31.648 +08:00 [WRN] 设备 2001 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:29:31.649 +08:00 [INF] === 性能报告结束 ===
2025-05-26 11:29:34.230 +08:00 [ERR] 重连设备 1001 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 11:29:34.230 +08:00 [ERR] 重连设备 1006 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 11:29:34.230 +08:00 [ERR] 重连设备 1002 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 11:29:34.230 +08:00 [ERR] 重连设备 1005 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 11:29:34.230 +08:00 [ERR] 重连设备 1003 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 11:29:34.230 +08:00 [ERR] 重连设备 1004 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 11:29:34.242 +08:00 [ERR] 重连设备 1010 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 11:29:34.242 +08:00 [ERR] 重连设备 1009 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 11:29:34.242 +08:00 [ERR] 重连设备 1007 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 11:29:34.242 +08:00 [ERR] 重连设备 1008 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 11:30:01.631 +08:00 [INF] === Modbus性能报告 ===
2025-05-26 11:30:01.632 +08:00 [INF] 设备 1001: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:30:01.633 +08:00 [WRN] 设备 1001 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:30:01.634 +08:00 [INF] 设备 1002: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:30:01.635 +08:00 [WRN] 设备 1002 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:30:01.636 +08:00 [INF] 设备 1003: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:30:01.637 +08:00 [WRN] 设备 1003 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:30:01.638 +08:00 [INF] 设备 1004: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:30:01.640 +08:00 [WRN] 设备 1004 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:30:01.641 +08:00 [INF] 设备 1005: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:30:01.642 +08:00 [WRN] 设备 1005 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:30:01.642 +08:00 [INF] 设备 1006: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:30:01.644 +08:00 [WRN] 设备 1006 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:30:01.644 +08:00 [INF] 设备 1007: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:30:01.645 +08:00 [WRN] 设备 1007 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:30:01.646 +08:00 [INF] 设备 1008: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:30:01.647 +08:00 [WRN] 设备 1008 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:30:01.648 +08:00 [INF] 设备 1009: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:30:01.649 +08:00 [WRN] 设备 1009 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:30:01.650 +08:00 [INF] 设备 1010: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:30:01.651 +08:00 [WRN] 设备 1010 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:30:01.652 +08:00 [INF] 设备 2001: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:30:01.653 +08:00 [WRN] 设备 2001 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:30:01.654 +08:00 [INF] === 性能报告结束 ===
2025-05-26 11:30:04.255 +08:00 [DBG] 开始监控请求: RequestId=1a93e0f0, DeviceId=1001, FunctionCode=0
2025-05-26 11:30:04.255 +08:00 [WRN] 设备 1001 健康检查失败，连续失败次数: 4
2025-05-26 11:30:04.256 +08:00 [DBG] 完成请求监控: RequestId=1a93e0f0, Success=false, TotalTime=0.6797ms, ExecutionTime=0.6797ms
2025-05-26 11:30:04.256 +08:00 [WRN] 请求失败: RequestId=1a93e0f0, DeviceId=1001, Error=Health check failed
2025-05-26 11:30:04.256 +08:00 [INF] 尝试重连设备 1001
2025-05-26 11:30:04.257 +08:00 [DBG] 开始监控请求: RequestId=f8138599, DeviceId=1002, FunctionCode=0
2025-05-26 11:30:04.257 +08:00 [WRN] 设备 1002 健康检查失败，连续失败次数: 4
2025-05-26 11:30:04.257 +08:00 [DBG] 完成请求监控: RequestId=f8138599, Success=false, TotalTime=0.4969ms, ExecutionTime=0.4969ms
2025-05-26 11:30:04.257 +08:00 [WRN] 请求失败: RequestId=f8138599, DeviceId=1002, Error=Health check failed
2025-05-26 11:30:04.258 +08:00 [INF] 尝试重连设备 1002
2025-05-26 11:30:04.258 +08:00 [DBG] 开始监控请求: RequestId=8502e6c8, DeviceId=1003, FunctionCode=0
2025-05-26 11:30:04.258 +08:00 [WRN] 设备 1003 健康检查失败，连续失败次数: 4
2025-05-26 11:30:04.259 +08:00 [DBG] 完成请求监控: RequestId=8502e6c8, Success=false, TotalTime=0.497ms, ExecutionTime=0.497ms
2025-05-26 11:30:04.259 +08:00 [WRN] 请求失败: RequestId=8502e6c8, DeviceId=1003, Error=Health check failed
2025-05-26 11:30:04.259 +08:00 [INF] 尝试重连设备 1003
2025-05-26 11:30:04.260 +08:00 [DBG] 开始监控请求: RequestId=ec9e5025, DeviceId=1004, FunctionCode=0
2025-05-26 11:30:04.260 +08:00 [WRN] 设备 1004 健康检查失败，连续失败次数: 4
2025-05-26 11:30:04.260 +08:00 [DBG] 完成请求监控: RequestId=ec9e5025, Success=false, TotalTime=0.5136ms, ExecutionTime=0.5136ms
2025-05-26 11:30:04.260 +08:00 [WRN] 请求失败: RequestId=ec9e5025, DeviceId=1004, Error=Health check failed
2025-05-26 11:30:04.261 +08:00 [INF] 尝试重连设备 1004
2025-05-26 11:30:04.261 +08:00 [DBG] 开始监控请求: RequestId=c0eb120d, DeviceId=1005, FunctionCode=0
2025-05-26 11:30:04.261 +08:00 [WRN] 设备 1005 健康检查失败，连续失败次数: 4
2025-05-26 11:30:04.263 +08:00 [DBG] 完成请求监控: RequestId=c0eb120d, Success=false, TotalTime=1.5023ms, ExecutionTime=1.5023ms
2025-05-26 11:30:04.263 +08:00 [WRN] 请求失败: RequestId=c0eb120d, DeviceId=1005, Error=Health check failed
2025-05-26 11:30:04.264 +08:00 [INF] 尝试重连设备 1005
2025-05-26 11:30:04.265 +08:00 [DBG] 开始监控请求: RequestId=9e8143e1, DeviceId=1006, FunctionCode=0
2025-05-26 11:30:04.265 +08:00 [WRN] 设备 1006 健康检查失败，连续失败次数: 4
2025-05-26 11:30:04.265 +08:00 [DBG] 完成请求监控: RequestId=9e8143e1, Success=false, TotalTime=0.6707ms, ExecutionTime=0.6707ms
2025-05-26 11:30:04.265 +08:00 [WRN] 请求失败: RequestId=9e8143e1, DeviceId=1006, Error=Health check failed
2025-05-26 11:30:04.266 +08:00 [INF] 尝试重连设备 1006
2025-05-26 11:30:04.266 +08:00 [DBG] 开始监控请求: RequestId=812afab0, DeviceId=1007, FunctionCode=0
2025-05-26 11:30:04.266 +08:00 [WRN] 设备 1007 健康检查失败，连续失败次数: 4
2025-05-26 11:30:04.267 +08:00 [DBG] 完成请求监控: RequestId=812afab0, Success=false, TotalTime=0.7309ms, ExecutionTime=0.7309ms
2025-05-26 11:30:04.267 +08:00 [WRN] 请求失败: RequestId=812afab0, DeviceId=1007, Error=Health check failed
2025-05-26 11:30:04.268 +08:00 [INF] 尝试重连设备 1007
2025-05-26 11:30:04.268 +08:00 [DBG] 开始监控请求: RequestId=4d20536e, DeviceId=1008, FunctionCode=0
2025-05-26 11:30:04.268 +08:00 [WRN] 设备 1008 健康检查失败，连续失败次数: 4
2025-05-26 11:30:04.269 +08:00 [DBG] 完成请求监控: RequestId=4d20536e, Success=false, TotalTime=0.5177ms, ExecutionTime=0.5177ms
2025-05-26 11:30:04.269 +08:00 [WRN] 请求失败: RequestId=4d20536e, DeviceId=1008, Error=Health check failed
2025-05-26 11:30:04.269 +08:00 [INF] 尝试重连设备 1008
2025-05-26 11:30:04.270 +08:00 [DBG] 开始监控请求: RequestId=0c002146, DeviceId=1009, FunctionCode=0
2025-05-26 11:30:04.270 +08:00 [WRN] 设备 1009 健康检查失败，连续失败次数: 4
2025-05-26 11:30:04.270 +08:00 [DBG] 完成请求监控: RequestId=0c002146, Success=false, TotalTime=0.6214ms, ExecutionTime=0.6214ms
2025-05-26 11:30:04.270 +08:00 [WRN] 请求失败: RequestId=0c002146, DeviceId=1009, Error=Health check failed
2025-05-26 11:30:04.271 +08:00 [INF] 尝试重连设备 1009
2025-05-26 11:30:04.271 +08:00 [DBG] 开始监控请求: RequestId=61d5d887, DeviceId=1010, FunctionCode=0
2025-05-26 11:30:04.272 +08:00 [WRN] 设备 1010 健康检查失败，连续失败次数: 4
2025-05-26 11:30:04.272 +08:00 [DBG] 完成请求监控: RequestId=61d5d887, Success=false, TotalTime=0.4772ms, ExecutionTime=0.4772ms
2025-05-26 11:30:04.272 +08:00 [WRN] 请求失败: RequestId=61d5d887, DeviceId=1010, Error=Health check failed
2025-05-26 11:30:04.273 +08:00 [INF] 尝试重连设备 1010
2025-05-26 11:30:04.273 +08:00 [DBG] 开始监控请求: RequestId=7ff8657b, DeviceId=2001, FunctionCode=0
2025-05-26 11:30:04.273 +08:00 [DBG] 开始读取保持寄存器: SlaveId=255, StartAddress=0, Quantity=1
2025-05-26 11:30:09.486 +08:00 [DBG] 设备 2001 健康检查失败: The write timed out.
2025-05-26 11:30:09.486 +08:00 [WRN] 设备 2001 健康检查失败，连续失败次数: 4
2025-05-26 11:30:09.487 +08:00 [DBG] 完成请求监控: RequestId=7ff8657b, Success=false, TotalTime=5214.4887ms, ExecutionTime=5214.4887ms
2025-05-26 11:30:09.487 +08:00 [WRN] 请求失败: RequestId=7ff8657b, DeviceId=2001, Error=Health check failed
2025-05-26 11:30:09.489 +08:00 [INF] 尝试重连设备 2001
2025-05-26 11:30:10.498 +08:00 [INF] 成功重连RTU设备 2001 (COM3)
2025-05-26 11:30:26.309 +08:00 [ERR] 重连设备 1004 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 11:30:26.309 +08:00 [ERR] 重连设备 1001 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 11:30:26.309 +08:00 [ERR] 重连设备 1002 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 11:30:26.309 +08:00 [ERR] 重连设备 1006 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 11:30:26.309 +08:00 [ERR] 重连设备 1005 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 11:30:26.309 +08:00 [ERR] 重连设备 1003 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 11:30:26.324 +08:00 [ERR] 重连设备 1010 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 11:30:26.324 +08:00 [ERR] 重连设备 1008 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 11:30:26.340 +08:00 [ERR] 重连设备 1009 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 11:30:26.340 +08:00 [ERR] 重连设备 1007 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 11:30:31.633 +08:00 [INF] === Modbus性能报告 ===
2025-05-26 11:30:31.636 +08:00 [INF] 设备 1001: 总请求=4, 成功=0, 失败=4, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:30:31.638 +08:00 [WRN] 设备 1001 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:30:31.639 +08:00 [INF] 设备 1002: 总请求=4, 成功=0, 失败=4, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:30:31.641 +08:00 [WRN] 设备 1002 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:30:31.642 +08:00 [INF] 设备 1003: 总请求=4, 成功=0, 失败=4, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:30:31.643 +08:00 [WRN] 设备 1003 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:30:31.643 +08:00 [INF] 设备 1004: 总请求=4, 成功=0, 失败=4, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:30:31.645 +08:00 [WRN] 设备 1004 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:30:31.646 +08:00 [INF] 设备 1005: 总请求=4, 成功=0, 失败=4, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:30:31.647 +08:00 [WRN] 设备 1005 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:30:31.648 +08:00 [INF] 设备 1006: 总请求=4, 成功=0, 失败=4, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:30:31.649 +08:00 [WRN] 设备 1006 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:30:31.650 +08:00 [INF] 设备 1007: 总请求=4, 成功=0, 失败=4, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:30:31.651 +08:00 [WRN] 设备 1007 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:30:31.652 +08:00 [INF] 设备 1008: 总请求=4, 成功=0, 失败=4, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:30:31.653 +08:00 [WRN] 设备 1008 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:30:31.653 +08:00 [INF] 设备 1009: 总请求=4, 成功=0, 失败=4, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:30:31.655 +08:00 [WRN] 设备 1009 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:30:31.655 +08:00 [INF] 设备 1010: 总请求=4, 成功=0, 失败=4, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:30:31.656 +08:00 [WRN] 设备 1010 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:30:31.657 +08:00 [INF] 设备 2001: 总请求=4, 成功=0, 失败=4, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:30:31.658 +08:00 [WRN] 设备 2001 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:30:31.659 +08:00 [INF] === 性能报告结束 ===
2025-05-26 11:30:56.359 +08:00 [DBG] 开始监控请求: RequestId=ac6c77b4, DeviceId=1001, FunctionCode=0
2025-05-26 11:30:56.359 +08:00 [WRN] 设备 1001 健康检查失败，连续失败次数: 5
2025-05-26 11:30:56.360 +08:00 [DBG] 完成请求监控: RequestId=ac6c77b4, Success=false, TotalTime=1.0306ms, ExecutionTime=1.0306ms
2025-05-26 11:30:56.360 +08:00 [WRN] 请求失败: RequestId=ac6c77b4, DeviceId=1001, Error=Health check failed
2025-05-26 11:30:56.361 +08:00 [INF] 尝试重连设备 1001
2025-05-26 11:30:56.361 +08:00 [DBG] 开始监控请求: RequestId=4ed5bb84, DeviceId=1002, FunctionCode=0
2025-05-26 11:30:56.361 +08:00 [WRN] 设备 1002 健康检查失败，连续失败次数: 5
2025-05-26 11:30:56.362 +08:00 [DBG] 完成请求监控: RequestId=4ed5bb84, Success=false, TotalTime=1.185ms, ExecutionTime=1.185ms
2025-05-26 11:30:56.362 +08:00 [WRN] 请求失败: RequestId=4ed5bb84, DeviceId=1002, Error=Health check failed
2025-05-26 11:30:56.363 +08:00 [INF] 尝试重连设备 1002
2025-05-26 11:30:56.364 +08:00 [DBG] 开始监控请求: RequestId=779faa8e, DeviceId=1003, FunctionCode=0
2025-05-26 11:30:56.364 +08:00 [WRN] 设备 1003 健康检查失败，连续失败次数: 5
2025-05-26 11:30:56.364 +08:00 [DBG] 完成请求监控: RequestId=779faa8e, Success=false, TotalTime=0.5387ms, ExecutionTime=0.5387ms
2025-05-26 11:30:56.364 +08:00 [WRN] 请求失败: RequestId=779faa8e, DeviceId=1003, Error=Health check failed
2025-05-26 11:30:56.365 +08:00 [INF] 尝试重连设备 1003
2025-05-26 11:30:56.366 +08:00 [DBG] 开始监控请求: RequestId=fb10b1ef, DeviceId=1004, FunctionCode=0
2025-05-26 11:30:56.366 +08:00 [WRN] 设备 1004 健康检查失败，连续失败次数: 5
2025-05-26 11:30:56.366 +08:00 [DBG] 完成请求监控: RequestId=fb10b1ef, Success=false, TotalTime=0.6338ms, ExecutionTime=0.6338ms
2025-05-26 11:30:56.366 +08:00 [WRN] 请求失败: RequestId=fb10b1ef, DeviceId=1004, Error=Health check failed
2025-05-26 11:30:56.367 +08:00 [INF] 尝试重连设备 1004
2025-05-26 11:30:56.367 +08:00 [DBG] 开始监控请求: RequestId=c8c5db83, DeviceId=1005, FunctionCode=0
2025-05-26 11:30:56.367 +08:00 [WRN] 设备 1005 健康检查失败，连续失败次数: 5
2025-05-26 11:30:56.368 +08:00 [DBG] 完成请求监控: RequestId=c8c5db83, Success=false, TotalTime=0.6035ms, ExecutionTime=0.6035ms
2025-05-26 11:30:56.368 +08:00 [WRN] 请求失败: RequestId=c8c5db83, DeviceId=1005, Error=Health check failed
2025-05-26 11:30:56.369 +08:00 [INF] 尝试重连设备 1005
2025-05-26 11:30:56.369 +08:00 [DBG] 开始监控请求: RequestId=36b136e7, DeviceId=1006, FunctionCode=0
2025-05-26 11:30:56.369 +08:00 [WRN] 设备 1006 健康检查失败，连续失败次数: 5
2025-05-26 11:30:56.370 +08:00 [DBG] 完成请求监控: RequestId=36b136e7, Success=false, TotalTime=0.6038ms, ExecutionTime=0.6038ms
2025-05-26 11:30:56.370 +08:00 [WRN] 请求失败: RequestId=36b136e7, DeviceId=1006, Error=Health check failed
2025-05-26 11:30:56.370 +08:00 [INF] 尝试重连设备 1006
2025-05-26 11:30:56.371 +08:00 [DBG] 开始监控请求: RequestId=c893d780, DeviceId=1007, FunctionCode=0
2025-05-26 11:30:56.371 +08:00 [WRN] 设备 1007 健康检查失败，连续失败次数: 5
2025-05-26 11:30:56.371 +08:00 [DBG] 完成请求监控: RequestId=c893d780, Success=false, TotalTime=0.626ms, ExecutionTime=0.626ms
2025-05-26 11:30:56.371 +08:00 [WRN] 请求失败: RequestId=c893d780, DeviceId=1007, Error=Health check failed
2025-05-26 11:30:56.372 +08:00 [INF] 尝试重连设备 1007
2025-05-26 11:30:56.372 +08:00 [DBG] 开始监控请求: RequestId=da7c0dc3, DeviceId=1008, FunctionCode=0
2025-05-26 11:30:56.372 +08:00 [WRN] 设备 1008 健康检查失败，连续失败次数: 5
2025-05-26 11:30:56.373 +08:00 [DBG] 完成请求监控: RequestId=da7c0dc3, Success=false, TotalTime=0.5231ms, ExecutionTime=0.5231ms
2025-05-26 11:30:56.373 +08:00 [WRN] 请求失败: RequestId=da7c0dc3, DeviceId=1008, Error=Health check failed
2025-05-26 11:30:56.374 +08:00 [INF] 尝试重连设备 1008
2025-05-26 11:30:56.374 +08:00 [DBG] 开始监控请求: RequestId=cb91e57c, DeviceId=1009, FunctionCode=0
2025-05-26 11:30:56.374 +08:00 [WRN] 设备 1009 健康检查失败，连续失败次数: 5
2025-05-26 11:30:56.376 +08:00 [DBG] 完成请求监控: RequestId=cb91e57c, Success=false, TotalTime=1.4861ms, ExecutionTime=1.4861ms
2025-05-26 11:30:56.376 +08:00 [WRN] 请求失败: RequestId=cb91e57c, DeviceId=1009, Error=Health check failed
2025-05-26 11:30:56.377 +08:00 [INF] 尝试重连设备 1009
2025-05-26 11:30:56.378 +08:00 [DBG] 开始监控请求: RequestId=0685f4c4, DeviceId=1010, FunctionCode=0
2025-05-26 11:30:56.378 +08:00 [WRN] 设备 1010 健康检查失败，连续失败次数: 5
2025-05-26 11:30:56.378 +08:00 [DBG] 完成请求监控: RequestId=0685f4c4, Success=false, TotalTime=0.5864ms, ExecutionTime=0.5864ms
2025-05-26 11:30:56.378 +08:00 [WRN] 请求失败: RequestId=0685f4c4, DeviceId=1010, Error=Health check failed
2025-05-26 11:30:56.379 +08:00 [INF] 尝试重连设备 1010
2025-05-26 11:30:56.379 +08:00 [DBG] 开始监控请求: RequestId=9c04f87d, DeviceId=2001, FunctionCode=0
2025-05-26 11:30:56.379 +08:00 [DBG] 开始读取保持寄存器: SlaveId=255, StartAddress=0, Quantity=1
2025-05-26 11:31:01.570 +08:00 [DBG] 设备 2001 健康检查失败: The write timed out.
2025-05-26 11:31:01.570 +08:00 [WRN] 设备 2001 健康检查失败，连续失败次数: 5
2025-05-26 11:31:01.571 +08:00 [DBG] 完成请求监控: RequestId=9c04f87d, Success=false, TotalTime=5191.6502ms, ExecutionTime=5191.6502ms
2025-05-26 11:31:01.571 +08:00 [WRN] 请求失败: RequestId=9c04f87d, DeviceId=2001, Error=Health check failed
2025-05-26 11:31:01.572 +08:00 [INF] 尝试重连设备 2001
2025-05-26 11:31:01.633 +08:00 [INF] === Modbus性能报告 ===
2025-05-26 11:31:01.633 +08:00 [INF] 设备 1001: 总请求=5, 成功=0, 失败=5, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:31:01.634 +08:00 [WRN] 设备 1001 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:31:01.635 +08:00 [INF] 设备 1002: 总请求=5, 成功=0, 失败=5, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:31:01.636 +08:00 [WRN] 设备 1002 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:31:01.637 +08:00 [INF] 设备 1003: 总请求=5, 成功=0, 失败=5, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:31:01.638 +08:00 [WRN] 设备 1003 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:31:01.639 +08:00 [INF] 设备 1004: 总请求=5, 成功=0, 失败=5, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:31:01.640 +08:00 [WRN] 设备 1004 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:31:01.640 +08:00 [INF] 设备 1005: 总请求=5, 成功=0, 失败=5, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:31:01.641 +08:00 [WRN] 设备 1005 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:31:01.642 +08:00 [INF] 设备 1006: 总请求=5, 成功=0, 失败=5, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:31:01.643 +08:00 [WRN] 设备 1006 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:31:01.644 +08:00 [INF] 设备 1007: 总请求=5, 成功=0, 失败=5, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:31:01.645 +08:00 [WRN] 设备 1007 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:31:01.646 +08:00 [INF] 设备 1008: 总请求=5, 成功=0, 失败=5, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:31:01.647 +08:00 [WRN] 设备 1008 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:31:01.647 +08:00 [INF] 设备 1009: 总请求=5, 成功=0, 失败=5, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:31:01.649 +08:00 [WRN] 设备 1009 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:31:01.649 +08:00 [INF] 设备 1010: 总请求=5, 成功=0, 失败=5, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:31:01.651 +08:00 [WRN] 设备 1010 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:31:01.651 +08:00 [INF] 设备 2001: 总请求=5, 成功=0, 失败=5, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:31:01.653 +08:00 [WRN] 设备 2001 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:31:01.653 +08:00 [INF] === 性能报告结束 ===
2025-05-26 11:31:02.584 +08:00 [INF] 成功重连RTU设备 2001 (COM3)
2025-05-26 11:31:10.052 +08:00 [INF] Application is shutting down...
2025-05-26 11:31:59.206 +08:00 [INF] 正在验证配置...
2025-05-26 11:31:59.233 +08:00 [INF] 配置验证通过
2025-05-26 11:31:59.283 +08:00 [INF] 正在启动Modbus TCP网关服务...
2025-05-26 11:31:59.286 +08:00 [INF] 创建Modbus客户端：ID=1001, IP=***************, Port=8080
2025-05-26 11:31:59.292 +08:00 [DBG] 已注册设备 1001 的线程安全客户端包装器
2025-05-26 11:31:59.292 +08:00 [INF] 已注册设备 1001 到线程安全连接池
2025-05-26 11:31:59.292 +08:00 [INF] 创建Modbus客户端：ID=1002, IP=***************, Port=8080
2025-05-26 11:31:59.293 +08:00 [DBG] 已注册设备 1002 的线程安全客户端包装器
2025-05-26 11:31:59.293 +08:00 [INF] 已注册设备 1002 到线程安全连接池
2025-05-26 11:31:59.294 +08:00 [INF] 创建Modbus客户端：ID=1003, IP=***************, Port=8080
2025-05-26 11:31:59.295 +08:00 [DBG] 已注册设备 1003 的线程安全客户端包装器
2025-05-26 11:31:59.295 +08:00 [INF] 已注册设备 1003 到线程安全连接池
2025-05-26 11:31:59.295 +08:00 [INF] 创建Modbus客户端：ID=1004, IP=***************, Port=8080
2025-05-26 11:31:59.296 +08:00 [DBG] 已注册设备 1004 的线程安全客户端包装器
2025-05-26 11:31:59.296 +08:00 [INF] 已注册设备 1004 到线程安全连接池
2025-05-26 11:31:59.296 +08:00 [INF] 创建Modbus客户端：ID=1005, IP=***************, Port=8080
2025-05-26 11:31:59.297 +08:00 [DBG] 已注册设备 1005 的线程安全客户端包装器
2025-05-26 11:31:59.297 +08:00 [INF] 已注册设备 1005 到线程安全连接池
2025-05-26 11:31:59.297 +08:00 [INF] 创建Modbus客户端：ID=1006, IP=***************, Port=8080
2025-05-26 11:31:59.298 +08:00 [DBG] 已注册设备 1006 的线程安全客户端包装器
2025-05-26 11:31:59.298 +08:00 [INF] 已注册设备 1006 到线程安全连接池
2025-05-26 11:31:59.299 +08:00 [INF] 创建Modbus客户端：ID=1007, IP=***************, Port=8080
2025-05-26 11:31:59.299 +08:00 [DBG] 已注册设备 1007 的线程安全客户端包装器
2025-05-26 11:31:59.299 +08:00 [INF] 已注册设备 1007 到线程安全连接池
2025-05-26 11:31:59.300 +08:00 [INF] 创建Modbus客户端：ID=1008, IP=***************, Port=8080
2025-05-26 11:31:59.301 +08:00 [DBG] 已注册设备 1008 的线程安全客户端包装器
2025-05-26 11:31:59.301 +08:00 [INF] 已注册设备 1008 到线程安全连接池
2025-05-26 11:31:59.301 +08:00 [INF] 创建Modbus客户端：ID=1009, IP=***************, Port=8080
2025-05-26 11:31:59.302 +08:00 [DBG] 已注册设备 1009 的线程安全客户端包装器
2025-05-26 11:31:59.302 +08:00 [INF] 已注册设备 1009 到线程安全连接池
2025-05-26 11:31:59.302 +08:00 [INF] 创建Modbus客户端：ID=1010, IP=***************, Port=8080
2025-05-26 11:31:59.303 +08:00 [DBG] 已注册设备 1010 的线程安全客户端包装器
2025-05-26 11:31:59.303 +08:00 [INF] 已注册设备 1010 到线程安全连接池
2025-05-26 11:31:59.304 +08:00 [INF] 创建Modbus客户端并连接到设备：ID=2001, PortName=COM3, BaudRate=9600
2025-05-26 11:31:59.310 +08:00 [DBG] 已注册设备 2001 的线程安全客户端包装器
2025-05-26 11:31:59.310 +08:00 [INF] 已注册设备 2001 到线程安全连接池
2025-05-26 11:31:59.313 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8080
2025-05-26 11:31:59.314 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8081
2025-05-26 11:31:59.314 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8082
2025-05-26 11:31:59.315 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8083
2025-05-26 11:31:59.315 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8084
2025-05-26 11:31:59.316 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8085
2025-05-26 11:31:59.316 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8086
2025-05-26 11:31:59.317 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8087
2025-05-26 11:31:59.318 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8088
2025-05-26 11:31:59.318 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8089
2025-05-26 11:31:59.320 +08:00 [INF] Modbus连接健康检查服务已启动
2025-05-26 11:31:59.322 +08:00 [DBG] 开始监控请求: RequestId=fd1ec9ca, DeviceId=1001, FunctionCode=0
2025-05-26 11:31:59.323 +08:00 [WRN] 设备 1001 健康检查失败，连续失败次数: 1
2025-05-26 11:31:59.324 +08:00 [DBG] 完成请求监控: RequestId=fd1ec9ca, Success=false, TotalTime=2.185ms, ExecutionTime=2.185ms
2025-05-26 11:31:59.325 +08:00 [WRN] 请求失败: RequestId=fd1ec9ca, DeviceId=1001, Error=Health check failed
2025-05-26 11:31:59.326 +08:00 [DBG] 开始监控请求: RequestId=06b8330c, DeviceId=1002, FunctionCode=0
2025-05-26 11:31:59.326 +08:00 [WRN] 设备 1002 健康检查失败，连续失败次数: 1
2025-05-26 11:31:59.326 +08:00 [DBG] 完成请求监控: RequestId=06b8330c, Success=false, TotalTime=0.5668ms, ExecutionTime=0.5668ms
2025-05-26 11:31:59.326 +08:00 [WRN] 请求失败: RequestId=06b8330c, DeviceId=1002, Error=Health check failed
2025-05-26 11:31:59.327 +08:00 [DBG] 开始监控请求: RequestId=5bb250ae, DeviceId=1003, FunctionCode=0
2025-05-26 11:31:59.327 +08:00 [WRN] 设备 1003 健康检查失败，连续失败次数: 1
2025-05-26 11:31:59.328 +08:00 [DBG] 完成请求监控: RequestId=5bb250ae, Success=false, TotalTime=0.5229ms, ExecutionTime=0.5229ms
2025-05-26 11:31:59.328 +08:00 [WRN] 请求失败: RequestId=5bb250ae, DeviceId=1003, Error=Health check failed
2025-05-26 11:31:59.328 +08:00 [DBG] 开始监控请求: RequestId=dd8cb72a, DeviceId=1004, FunctionCode=0
2025-05-26 11:31:59.328 +08:00 [WRN] 设备 1004 健康检查失败，连续失败次数: 1
2025-05-26 11:31:59.329 +08:00 [DBG] 完成请求监控: RequestId=dd8cb72a, Success=false, TotalTime=0.5927ms, ExecutionTime=0.5927ms
2025-05-26 11:31:59.329 +08:00 [WRN] 请求失败: RequestId=dd8cb72a, DeviceId=1004, Error=Health check failed
2025-05-26 11:31:59.329 +08:00 [DBG] 开始监控请求: RequestId=98cfccc0, DeviceId=1005, FunctionCode=0
2025-05-26 11:31:59.329 +08:00 [WRN] 设备 1005 健康检查失败，连续失败次数: 1
2025-05-26 11:31:59.330 +08:00 [DBG] 完成请求监控: RequestId=98cfccc0, Success=false, TotalTime=0.5651ms, ExecutionTime=0.5651ms
2025-05-26 11:31:59.330 +08:00 [WRN] 请求失败: RequestId=98cfccc0, DeviceId=1005, Error=Health check failed
2025-05-26 11:31:59.331 +08:00 [DBG] 开始监控请求: RequestId=36269d35, DeviceId=1006, FunctionCode=0
2025-05-26 11:31:59.331 +08:00 [WRN] 设备 1006 健康检查失败，连续失败次数: 1
2025-05-26 11:31:59.332 +08:00 [DBG] 完成请求监控: RequestId=36269d35, Success=false, TotalTime=0.5929ms, ExecutionTime=0.5929ms
2025-05-26 11:31:59.332 +08:00 [WRN] 请求失败: RequestId=36269d35, DeviceId=1006, Error=Health check failed
2025-05-26 11:31:59.333 +08:00 [DBG] 开始监控请求: RequestId=67906506, DeviceId=1007, FunctionCode=0
2025-05-26 11:31:59.333 +08:00 [WRN] 设备 1007 健康检查失败，连续失败次数: 1
2025-05-26 11:31:59.333 +08:00 [DBG] 完成请求监控: RequestId=67906506, Success=false, TotalTime=0.5727ms, ExecutionTime=0.5727ms
2025-05-26 11:31:59.333 +08:00 [WRN] 请求失败: RequestId=67906506, DeviceId=1007, Error=Health check failed
2025-05-26 11:31:59.334 +08:00 [DBG] 开始监控请求: RequestId=3b1e6d2c, DeviceId=1008, FunctionCode=0
2025-05-26 11:31:59.334 +08:00 [WRN] 设备 1008 健康检查失败，连续失败次数: 1
2025-05-26 11:31:59.335 +08:00 [DBG] 完成请求监控: RequestId=3b1e6d2c, Success=false, TotalTime=1.1371ms, ExecutionTime=1.1371ms
2025-05-26 11:31:59.335 +08:00 [WRN] 请求失败: RequestId=3b1e6d2c, DeviceId=1008, Error=Health check failed
2025-05-26 11:31:59.336 +08:00 [DBG] 开始监控请求: RequestId=ad54f917, DeviceId=1009, FunctionCode=0
2025-05-26 11:31:59.336 +08:00 [WRN] 设备 1009 健康检查失败，连续失败次数: 1
2025-05-26 11:31:59.337 +08:00 [DBG] 完成请求监控: RequestId=ad54f917, Success=false, TotalTime=0.722ms, ExecutionTime=0.722ms
2025-05-26 11:31:59.337 +08:00 [WRN] 请求失败: RequestId=ad54f917, DeviceId=1009, Error=Health check failed
2025-05-26 11:31:59.338 +08:00 [DBG] 开始监控请求: RequestId=fffd80f1, DeviceId=1010, FunctionCode=0
2025-05-26 11:31:59.338 +08:00 [WRN] 设备 1010 健康检查失败，连续失败次数: 1
2025-05-26 11:31:59.338 +08:00 [DBG] 完成请求监控: RequestId=fffd80f1, Success=false, TotalTime=0.5071ms, ExecutionTime=0.5071ms
2025-05-26 11:31:59.338 +08:00 [WRN] 请求失败: RequestId=fffd80f1, DeviceId=1010, Error=Health check failed
2025-05-26 11:31:59.339 +08:00 [DBG] 开始监控请求: RequestId=739a6bed, DeviceId=2001, FunctionCode=0
2025-05-26 11:31:59.340 +08:00 [DBG] 开始读取保持寄存器: SlaveId=255, StartAddress=0, Quantity=1
2025-05-26 11:31:59.365 +08:00 [INF] Now listening on: http://localhost:5000
2025-05-26 11:31:59.366 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-05-26 11:31:59.366 +08:00 [INF] Hosting environment: Production
2025-05-26 11:31:59.367 +08:00 [INF] Content root path: C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway
2025-05-26 11:32:04.571 +08:00 [DBG] 设备 2001 健康检查失败: The write timed out.
2025-05-26 11:32:04.571 +08:00 [WRN] 设备 2001 健康检查失败，连续失败次数: 1
2025-05-26 11:32:04.572 +08:00 [DBG] 完成请求监控: RequestId=739a6bed, Success=false, TotalTime=5233.123ms, ExecutionTime=5233.123ms
2025-05-26 11:32:04.572 +08:00 [WRN] 请求失败: RequestId=739a6bed, DeviceId=2001, Error=Health check failed
2025-05-26 11:32:13.909 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/monitoring/overview - null null
2025-05-26 11:32:13.921 +08:00 [INF] Executing endpoint 'Gsdt.ModbusGateway.Controllers.MonitoringController.GetSystemOverview (Gsdt.ModbusGateway)'
2025-05-26 11:32:13.932 +08:00 [INF] Route matched with {action = "GetSystemOverview", controller = "Monitoring"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[Gsdt.ModbusGateway.Controllers.SystemOverview] GetSystemOverview() on controller Gsdt.ModbusGateway.Controllers.MonitoringController (Gsdt.ModbusGateway).
2025-05-26 11:32:13.940 +08:00 [INF] Executing OkObjectResult, writing value of type 'Gsdt.ModbusGateway.Controllers.SystemOverview'.
2025-05-26 11:32:13.944 +08:00 [INF] Executed action Gsdt.ModbusGateway.Controllers.MonitoringController.GetSystemOverview (Gsdt.ModbusGateway) in 9.4883ms
2025-05-26 11:32:13.945 +08:00 [INF] Executed endpoint 'Gsdt.ModbusGateway.Controllers.MonitoringController.GetSystemOverview (Gsdt.ModbusGateway)'
2025-05-26 11:32:13.947 +08:00 [ERR] Connection id "0HNCS0NK1A1HR", Request id "0HNCS0NK1A1HR:00000001": An unhandled exception was thrown by the application.
System.NotSupportedException: JsonTypeInfo metadata for type 'Gsdt.ModbusGateway.Controllers.SystemOverview' was not provided by TypeInfoResolver of type '[]'. If using source generation, ensure that all root types passed to the serializer have been annotated with 'JsonSerializableAttribute', along with any types that might be serialized polymorphically.
   at System.Text.Json.ThrowHelper.ThrowNotSupportedException_NoMetadataForType(Type type, IJsonTypeInfoResolver resolver)
   at System.Text.Json.JsonSerializerOptions.GetTypeInfoInternal(Type type, Boolean ensureConfigured, Nullable`1 ensureNotNull, Boolean resolveIfMutable, Boolean fallBackToNearestAncestorType)
   at System.Text.Json.JsonSerializerOptions.GetTypeInfo(Type type)
   at Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter.WriteResponseBodyAsync(OutputFormatterWriteContext context, Encoding selectedEncoding)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http.HttpProtocol.ProcessRequests[TContext](IHttpApplication`1 application)
2025-05-26 11:32:13.955 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/monitoring/overview - 500 0 null 47.0845ms
2025-05-26 11:32:29.291 +08:00 [INF] === Modbus性能报告 ===
2025-05-26 11:32:29.294 +08:00 [INF] 设备 1001: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:32:29.296 +08:00 [WRN] 设备 1001 最近错误: Health check failed
2025-05-26 11:32:29.297 +08:00 [INF] 设备 1002: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:32:29.298 +08:00 [WRN] 设备 1002 最近错误: Health check failed
2025-05-26 11:32:29.299 +08:00 [INF] 设备 1003: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:32:29.300 +08:00 [WRN] 设备 1003 最近错误: Health check failed
2025-05-26 11:32:29.300 +08:00 [INF] 设备 1004: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:32:29.301 +08:00 [WRN] 设备 1004 最近错误: Health check failed
2025-05-26 11:32:29.302 +08:00 [INF] 设备 1005: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:32:29.304 +08:00 [WRN] 设备 1005 最近错误: Health check failed
2025-05-26 11:32:29.304 +08:00 [INF] 设备 1006: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:32:29.305 +08:00 [WRN] 设备 1006 最近错误: Health check failed
2025-05-26 11:32:29.306 +08:00 [INF] 设备 1007: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:32:29.308 +08:00 [WRN] 设备 1007 最近错误: Health check failed
2025-05-26 11:32:29.308 +08:00 [INF] 设备 1008: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:32:29.309 +08:00 [WRN] 设备 1008 最近错误: Health check failed
2025-05-26 11:32:29.310 +08:00 [INF] 设备 1009: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:32:29.311 +08:00 [WRN] 设备 1009 最近错误: Health check failed
2025-05-26 11:32:29.312 +08:00 [INF] 设备 1010: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:32:29.313 +08:00 [WRN] 设备 1010 最近错误: Health check failed
2025-05-26 11:32:29.314 +08:00 [INF] 设备 2001: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:32:29.315 +08:00 [WRN] 设备 2001 最近错误: Health check failed
2025-05-26 11:32:29.316 +08:00 [INF] === 性能报告结束 ===
2025-05-26 11:32:34.583 +08:00 [DBG] 开始监控请求: RequestId=ce19ec9d, DeviceId=1001, FunctionCode=0
2025-05-26 11:32:34.584 +08:00 [WRN] 设备 1001 健康检查失败，连续失败次数: 2
2025-05-26 11:32:34.585 +08:00 [DBG] 完成请求监控: RequestId=ce19ec9d, Success=false, TotalTime=1.4651ms, ExecutionTime=1.4651ms
2025-05-26 11:32:34.585 +08:00 [WRN] 请求失败: RequestId=ce19ec9d, DeviceId=1001, Error=Health check failed
2025-05-26 11:32:34.586 +08:00 [DBG] 开始监控请求: RequestId=c3a60b13, DeviceId=1002, FunctionCode=0
2025-05-26 11:32:34.586 +08:00 [WRN] 设备 1002 健康检查失败，连续失败次数: 2
2025-05-26 11:32:34.587 +08:00 [DBG] 完成请求监控: RequestId=c3a60b13, Success=false, TotalTime=1.6079ms, ExecutionTime=1.6079ms
2025-05-26 11:32:34.587 +08:00 [WRN] 请求失败: RequestId=c3a60b13, DeviceId=1002, Error=Health check failed
2025-05-26 11:32:34.588 +08:00 [DBG] 开始监控请求: RequestId=ad8f0de8, DeviceId=1003, FunctionCode=0
2025-05-26 11:32:34.588 +08:00 [WRN] 设备 1003 健康检查失败，连续失败次数: 2
2025-05-26 11:32:34.588 +08:00 [DBG] 完成请求监控: RequestId=ad8f0de8, Success=false, TotalTime=0.4906ms, ExecutionTime=0.4906ms
2025-05-26 11:32:34.588 +08:00 [WRN] 请求失败: RequestId=ad8f0de8, DeviceId=1003, Error=Health check failed
2025-05-26 11:32:34.589 +08:00 [DBG] 开始监控请求: RequestId=c7405010, DeviceId=1004, FunctionCode=0
2025-05-26 11:32:34.589 +08:00 [WRN] 设备 1004 健康检查失败，连续失败次数: 2
2025-05-26 11:32:34.590 +08:00 [DBG] 完成请求监控: RequestId=c7405010, Success=false, TotalTime=0.4782ms, ExecutionTime=0.4782ms
2025-05-26 11:32:34.590 +08:00 [WRN] 请求失败: RequestId=c7405010, DeviceId=1004, Error=Health check failed
2025-05-26 11:32:34.590 +08:00 [DBG] 开始监控请求: RequestId=3d94bc2a, DeviceId=1005, FunctionCode=0
2025-05-26 11:32:34.590 +08:00 [WRN] 设备 1005 健康检查失败，连续失败次数: 2
2025-05-26 11:32:34.591 +08:00 [DBG] 完成请求监控: RequestId=3d94bc2a, Success=false, TotalTime=0.5211ms, ExecutionTime=0.5211ms
2025-05-26 11:32:34.591 +08:00 [WRN] 请求失败: RequestId=3d94bc2a, DeviceId=1005, Error=Health check failed
2025-05-26 11:32:34.591 +08:00 [DBG] 开始监控请求: RequestId=dd932056, DeviceId=1006, FunctionCode=0
2025-05-26 11:32:34.591 +08:00 [WRN] 设备 1006 健康检查失败，连续失败次数: 2
2025-05-26 11:32:34.592 +08:00 [DBG] 完成请求监控: RequestId=dd932056, Success=false, TotalTime=0.4944ms, ExecutionTime=0.4944ms
2025-05-26 11:32:34.592 +08:00 [WRN] 请求失败: RequestId=dd932056, DeviceId=1006, Error=Health check failed
2025-05-26 11:32:34.592 +08:00 [DBG] 开始监控请求: RequestId=e2b17468, DeviceId=1007, FunctionCode=0
2025-05-26 11:32:34.592 +08:00 [WRN] 设备 1007 健康检查失败，连续失败次数: 2
2025-05-26 11:32:34.593 +08:00 [DBG] 完成请求监控: RequestId=e2b17468, Success=false, TotalTime=0.4618ms, ExecutionTime=0.4618ms
2025-05-26 11:32:34.593 +08:00 [WRN] 请求失败: RequestId=e2b17468, DeviceId=1007, Error=Health check failed
2025-05-26 11:32:34.594 +08:00 [DBG] 开始监控请求: RequestId=06a6cf47, DeviceId=1008, FunctionCode=0
2025-05-26 11:32:34.594 +08:00 [WRN] 设备 1008 健康检查失败，连续失败次数: 2
2025-05-26 11:32:34.594 +08:00 [DBG] 完成请求监控: RequestId=06a6cf47, Success=false, TotalTime=0.5703ms, ExecutionTime=0.5703ms
2025-05-26 11:32:34.594 +08:00 [WRN] 请求失败: RequestId=06a6cf47, DeviceId=1008, Error=Health check failed
2025-05-26 11:32:34.595 +08:00 [DBG] 开始监控请求: RequestId=36f88e68, DeviceId=1009, FunctionCode=0
2025-05-26 11:32:34.595 +08:00 [WRN] 设备 1009 健康检查失败，连续失败次数: 2
2025-05-26 11:32:34.595 +08:00 [DBG] 完成请求监控: RequestId=36f88e68, Success=false, TotalTime=0.5272ms, ExecutionTime=0.5272ms
2025-05-26 11:32:34.595 +08:00 [WRN] 请求失败: RequestId=36f88e68, DeviceId=1009, Error=Health check failed
2025-05-26 11:32:34.596 +08:00 [DBG] 开始监控请求: RequestId=38a7849a, DeviceId=1010, FunctionCode=0
2025-05-26 11:32:34.596 +08:00 [WRN] 设备 1010 健康检查失败，连续失败次数: 2
2025-05-26 11:32:34.596 +08:00 [DBG] 完成请求监控: RequestId=38a7849a, Success=false, TotalTime=0.4888ms, ExecutionTime=0.4888ms
2025-05-26 11:32:34.596 +08:00 [WRN] 请求失败: RequestId=38a7849a, DeviceId=1010, Error=Health check failed
2025-05-26 11:32:34.598 +08:00 [DBG] 开始监控请求: RequestId=0b983edb, DeviceId=2001, FunctionCode=0
2025-05-26 11:32:34.598 +08:00 [DBG] 开始读取保持寄存器: SlaveId=255, StartAddress=0, Quantity=1
2025-05-26 11:32:39.240 +08:00 [INF] Application is shutting down...
2025-05-26 11:32:39.809 +08:00 [DBG] 设备 2001 健康检查失败: The write timed out.
2025-05-26 11:32:39.809 +08:00 [WRN] 设备 2001 健康检查失败，连续失败次数: 2
2025-05-26 11:32:39.810 +08:00 [DBG] 完成请求监控: RequestId=0b983edb, Success=false, TotalTime=5212.0927ms, ExecutionTime=5212.0927ms
2025-05-26 11:32:39.810 +08:00 [WRN] 请求失败: RequestId=0b983edb, DeviceId=2001, Error=Health check failed
2025-05-26 11:32:39.810 +08:00 [INF] 连接健康检查服务正在停止
2025-05-26 11:32:39.812 +08:00 [INF] 正在停止Modbus TCP网关服务...
2025-05-26 11:32:39.813 +08:00 [INF] Modbus TCP网关服务已停止
2025-05-26 11:32:39.817 +08:00 [DBG] 正在释放线程安全ModbusClient连接池资源
2025-05-26 11:32:39.817 +08:00 [DBG] 线程安全ModbusClient连接池资源已释放
2025-05-26 11:32:39.818 +08:00 [INF] 正在输出最终性能报告...
2025-05-26 11:32:39.818 +08:00 [INF] === Modbus性能报告 ===
2025-05-26 11:32:39.818 +08:00 [INF] 设备 1001: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:32:39.820 +08:00 [WRN] 设备 1001 最近错误: Health check failed; Health check failed
2025-05-26 11:32:39.820 +08:00 [INF] 设备 1002: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:32:39.821 +08:00 [WRN] 设备 1002 最近错误: Health check failed; Health check failed
2025-05-26 11:32:39.822 +08:00 [INF] 设备 1003: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:32:39.823 +08:00 [WRN] 设备 1003 最近错误: Health check failed; Health check failed
2025-05-26 11:32:39.824 +08:00 [INF] 设备 1004: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:32:39.825 +08:00 [WRN] 设备 1004 最近错误: Health check failed; Health check failed
2025-05-26 11:32:39.826 +08:00 [INF] 设备 1005: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:32:39.827 +08:00 [WRN] 设备 1005 最近错误: Health check failed; Health check failed
2025-05-26 11:32:39.828 +08:00 [INF] 设备 1006: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:32:39.829 +08:00 [WRN] 设备 1006 最近错误: Health check failed; Health check failed
2025-05-26 11:32:39.830 +08:00 [INF] 设备 1007: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:32:39.831 +08:00 [WRN] 设备 1007 最近错误: Health check failed; Health check failed
2025-05-26 11:32:39.832 +08:00 [INF] 设备 1008: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:32:39.833 +08:00 [WRN] 设备 1008 最近错误: Health check failed; Health check failed
2025-05-26 11:32:39.833 +08:00 [INF] 设备 1009: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:32:39.835 +08:00 [WRN] 设备 1009 最近错误: Health check failed; Health check failed
2025-05-26 11:32:39.835 +08:00 [INF] 设备 1010: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:32:39.836 +08:00 [WRN] 设备 1010 最近错误: Health check failed; Health check failed
2025-05-26 11:32:39.837 +08:00 [INF] 设备 2001: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:32:39.838 +08:00 [WRN] 设备 2001 最近错误: Health check failed; Health check failed
2025-05-26 11:32:39.839 +08:00 [INF] === 性能报告结束 ===
2025-05-26 11:33:44.400 +08:00 [INF] 正在验证配置...
2025-05-26 11:33:44.426 +08:00 [INF] 配置验证通过
2025-05-26 11:33:44.474 +08:00 [INF] 正在启动Modbus TCP网关服务...
2025-05-26 11:33:44.477 +08:00 [INF] 创建Modbus客户端：ID=1001, IP=***************, Port=8080
2025-05-26 11:33:44.484 +08:00 [DBG] 已注册设备 1001 的线程安全客户端包装器
2025-05-26 11:33:44.484 +08:00 [INF] 已注册设备 1001 到线程安全连接池
2025-05-26 11:33:44.485 +08:00 [INF] 创建Modbus客户端：ID=1002, IP=***************, Port=8080
2025-05-26 11:33:44.486 +08:00 [DBG] 已注册设备 1002 的线程安全客户端包装器
2025-05-26 11:33:44.486 +08:00 [INF] 已注册设备 1002 到线程安全连接池
2025-05-26 11:33:44.486 +08:00 [INF] 创建Modbus客户端：ID=1003, IP=***************, Port=8080
2025-05-26 11:33:44.487 +08:00 [DBG] 已注册设备 1003 的线程安全客户端包装器
2025-05-26 11:33:44.487 +08:00 [INF] 已注册设备 1003 到线程安全连接池
2025-05-26 11:33:44.487 +08:00 [INF] 创建Modbus客户端：ID=1004, IP=***************, Port=8080
2025-05-26 11:33:44.488 +08:00 [DBG] 已注册设备 1004 的线程安全客户端包装器
2025-05-26 11:33:44.488 +08:00 [INF] 已注册设备 1004 到线程安全连接池
2025-05-26 11:33:44.488 +08:00 [INF] 创建Modbus客户端：ID=1005, IP=***************, Port=8080
2025-05-26 11:33:44.490 +08:00 [DBG] 已注册设备 1005 的线程安全客户端包装器
2025-05-26 11:33:44.490 +08:00 [INF] 已注册设备 1005 到线程安全连接池
2025-05-26 11:33:44.490 +08:00 [INF] 创建Modbus客户端：ID=1006, IP=***************, Port=8080
2025-05-26 11:33:44.491 +08:00 [DBG] 已注册设备 1006 的线程安全客户端包装器
2025-05-26 11:33:44.491 +08:00 [INF] 已注册设备 1006 到线程安全连接池
2025-05-26 11:33:44.492 +08:00 [INF] 创建Modbus客户端：ID=1007, IP=***************, Port=8080
2025-05-26 11:33:44.493 +08:00 [DBG] 已注册设备 1007 的线程安全客户端包装器
2025-05-26 11:33:44.493 +08:00 [INF] 已注册设备 1007 到线程安全连接池
2025-05-26 11:33:44.493 +08:00 [INF] 创建Modbus客户端：ID=1008, IP=***************, Port=8080
2025-05-26 11:33:44.494 +08:00 [DBG] 已注册设备 1008 的线程安全客户端包装器
2025-05-26 11:33:44.494 +08:00 [INF] 已注册设备 1008 到线程安全连接池
2025-05-26 11:33:44.495 +08:00 [INF] 创建Modbus客户端：ID=1009, IP=***************, Port=8080
2025-05-26 11:33:44.495 +08:00 [DBG] 已注册设备 1009 的线程安全客户端包装器
2025-05-26 11:33:44.495 +08:00 [INF] 已注册设备 1009 到线程安全连接池
2025-05-26 11:33:44.496 +08:00 [INF] 创建Modbus客户端：ID=1010, IP=***************, Port=8080
2025-05-26 11:33:44.497 +08:00 [DBG] 已注册设备 1010 的线程安全客户端包装器
2025-05-26 11:33:44.497 +08:00 [INF] 已注册设备 1010 到线程安全连接池
2025-05-26 11:33:44.497 +08:00 [INF] 创建Modbus客户端并连接到设备：ID=2001, PortName=COM3, BaudRate=9600
2025-05-26 11:33:44.505 +08:00 [DBG] 已注册设备 2001 的线程安全客户端包装器
2025-05-26 11:33:44.505 +08:00 [INF] 已注册设备 2001 到线程安全连接池
2025-05-26 11:33:44.509 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8080
2025-05-26 11:33:44.510 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8081
2025-05-26 11:33:44.511 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8082
2025-05-26 11:33:44.511 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8083
2025-05-26 11:33:44.512 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8084
2025-05-26 11:33:44.512 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8085
2025-05-26 11:33:44.513 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8086
2025-05-26 11:33:44.514 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8087
2025-05-26 11:33:44.515 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8088
2025-05-26 11:33:44.515 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8089
2025-05-26 11:33:44.518 +08:00 [INF] Modbus连接健康检查服务已启动
2025-05-26 11:33:44.521 +08:00 [DBG] 开始监控请求: RequestId=141f9108, DeviceId=1001, FunctionCode=0
2025-05-26 11:33:44.522 +08:00 [WRN] 设备 1001 健康检查失败，连续失败次数: 1
2025-05-26 11:33:44.523 +08:00 [DBG] 完成请求监控: RequestId=141f9108, Success=false, TotalTime=2.6142ms, ExecutionTime=2.6142ms
2025-05-26 11:33:44.524 +08:00 [WRN] 请求失败: RequestId=141f9108, DeviceId=1001, Error=Health check failed
2025-05-26 11:33:44.525 +08:00 [DBG] 开始监控请求: RequestId=b3d2053e, DeviceId=1002, FunctionCode=0
2025-05-26 11:33:44.525 +08:00 [WRN] 设备 1002 健康检查失败，连续失败次数: 1
2025-05-26 11:33:44.526 +08:00 [DBG] 完成请求监控: RequestId=b3d2053e, Success=false, TotalTime=0.592ms, ExecutionTime=0.592ms
2025-05-26 11:33:44.526 +08:00 [WRN] 请求失败: RequestId=b3d2053e, DeviceId=1002, Error=Health check failed
2025-05-26 11:33:44.526 +08:00 [DBG] 开始监控请求: RequestId=0ea870e6, DeviceId=1003, FunctionCode=0
2025-05-26 11:33:44.526 +08:00 [WRN] 设备 1003 健康检查失败，连续失败次数: 1
2025-05-26 11:33:44.527 +08:00 [DBG] 完成请求监控: RequestId=0ea870e6, Success=false, TotalTime=0.5584ms, ExecutionTime=0.5584ms
2025-05-26 11:33:44.527 +08:00 [WRN] 请求失败: RequestId=0ea870e6, DeviceId=1003, Error=Health check failed
2025-05-26 11:33:44.528 +08:00 [DBG] 开始监控请求: RequestId=ed0a895e, DeviceId=1004, FunctionCode=0
2025-05-26 11:33:44.528 +08:00 [WRN] 设备 1004 健康检查失败，连续失败次数: 1
2025-05-26 11:33:44.528 +08:00 [DBG] 完成请求监控: RequestId=ed0a895e, Success=false, TotalTime=0.59ms, ExecutionTime=0.59ms
2025-05-26 11:33:44.528 +08:00 [WRN] 请求失败: RequestId=ed0a895e, DeviceId=1004, Error=Health check failed
2025-05-26 11:33:44.529 +08:00 [DBG] 开始监控请求: RequestId=1ba98342, DeviceId=1005, FunctionCode=0
2025-05-26 11:33:44.529 +08:00 [WRN] 设备 1005 健康检查失败，连续失败次数: 1
2025-05-26 11:33:44.530 +08:00 [DBG] 完成请求监控: RequestId=1ba98342, Success=false, TotalTime=0.6936ms, ExecutionTime=0.6936ms
2025-05-26 11:33:44.530 +08:00 [WRN] 请求失败: RequestId=1ba98342, DeviceId=1005, Error=Health check failed
2025-05-26 11:33:44.531 +08:00 [DBG] 开始监控请求: RequestId=92d220e9, DeviceId=1006, FunctionCode=0
2025-05-26 11:33:44.531 +08:00 [WRN] 设备 1006 健康检查失败，连续失败次数: 1
2025-05-26 11:33:44.532 +08:00 [DBG] 完成请求监控: RequestId=92d220e9, Success=false, TotalTime=1.0666ms, ExecutionTime=1.0666ms
2025-05-26 11:33:44.532 +08:00 [WRN] 请求失败: RequestId=92d220e9, DeviceId=1006, Error=Health check failed
2025-05-26 11:33:44.533 +08:00 [DBG] 开始监控请求: RequestId=7ed8ea50, DeviceId=1007, FunctionCode=0
2025-05-26 11:33:44.533 +08:00 [WRN] 设备 1007 健康检查失败，连续失败次数: 1
2025-05-26 11:33:44.533 +08:00 [DBG] 完成请求监控: RequestId=7ed8ea50, Success=false, TotalTime=0.5775ms, ExecutionTime=0.5775ms
2025-05-26 11:33:44.533 +08:00 [WRN] 请求失败: RequestId=7ed8ea50, DeviceId=1007, Error=Health check failed
2025-05-26 11:33:44.534 +08:00 [DBG] 开始监控请求: RequestId=529b81ae, DeviceId=1008, FunctionCode=0
2025-05-26 11:33:44.534 +08:00 [WRN] 设备 1008 健康检查失败，连续失败次数: 1
2025-05-26 11:33:44.535 +08:00 [DBG] 完成请求监控: RequestId=529b81ae, Success=false, TotalTime=0.9319ms, ExecutionTime=0.9319ms
2025-05-26 11:33:44.535 +08:00 [WRN] 请求失败: RequestId=529b81ae, DeviceId=1008, Error=Health check failed
2025-05-26 11:33:44.535 +08:00 [DBG] 开始监控请求: RequestId=954fc951, DeviceId=1009, FunctionCode=0
2025-05-26 11:33:44.535 +08:00 [WRN] 设备 1009 健康检查失败，连续失败次数: 1
2025-05-26 11:33:44.536 +08:00 [DBG] 完成请求监控: RequestId=954fc951, Success=false, TotalTime=0.6465ms, ExecutionTime=0.6465ms
2025-05-26 11:33:44.536 +08:00 [WRN] 请求失败: RequestId=954fc951, DeviceId=1009, Error=Health check failed
2025-05-26 11:33:44.537 +08:00 [DBG] 开始监控请求: RequestId=949fd196, DeviceId=1010, FunctionCode=0
2025-05-26 11:33:44.537 +08:00 [WRN] 设备 1010 健康检查失败，连续失败次数: 1
2025-05-26 11:33:44.537 +08:00 [DBG] 完成请求监控: RequestId=949fd196, Success=false, TotalTime=0.608ms, ExecutionTime=0.608ms
2025-05-26 11:33:44.537 +08:00 [WRN] 请求失败: RequestId=949fd196, DeviceId=1010, Error=Health check failed
2025-05-26 11:33:44.538 +08:00 [DBG] 开始监控请求: RequestId=992bf0a0, DeviceId=2001, FunctionCode=0
2025-05-26 11:33:44.539 +08:00 [DBG] 开始读取保持寄存器: SlaveId=255, StartAddress=0, Quantity=1
2025-05-26 11:33:44.571 +08:00 [INF] Now listening on: http://localhost:5000
2025-05-26 11:33:44.573 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-05-26 11:33:44.574 +08:00 [INF] Hosting environment: Production
2025-05-26 11:33:44.574 +08:00 [INF] Content root path: C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway
2025-05-26 11:33:49.773 +08:00 [DBG] 设备 2001 健康检查失败: The write timed out.
2025-05-26 11:33:49.773 +08:00 [WRN] 设备 2001 健康检查失败，连续失败次数: 1
2025-05-26 11:33:49.774 +08:00 [DBG] 完成请求监控: RequestId=992bf0a0, Success=false, TotalTime=5235.834ms, ExecutionTime=5235.834ms
2025-05-26 11:33:49.774 +08:00 [WRN] 请求失败: RequestId=992bf0a0, DeviceId=2001, Error=Health check failed
2025-05-26 11:34:02.680 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/monitoring/overview - null null
2025-05-26 11:34:02.698 +08:00 [INF] Executing endpoint 'Gsdt.ModbusGateway.Controllers.MonitoringController.GetSystemOverview (Gsdt.ModbusGateway)'
2025-05-26 11:34:02.710 +08:00 [INF] Route matched with {action = "GetSystemOverview", controller = "Monitoring"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[Gsdt.ModbusGateway.Controllers.SystemOverview] GetSystemOverview() on controller Gsdt.ModbusGateway.Controllers.MonitoringController (Gsdt.ModbusGateway).
2025-05-26 11:34:02.718 +08:00 [INF] Executing OkObjectResult, writing value of type 'Gsdt.ModbusGateway.Controllers.SystemOverview'.
2025-05-26 11:34:02.724 +08:00 [INF] Executed action Gsdt.ModbusGateway.Controllers.MonitoringController.GetSystemOverview (Gsdt.ModbusGateway) in 10.1277ms
2025-05-26 11:34:02.726 +08:00 [INF] Executed endpoint 'Gsdt.ModbusGateway.Controllers.MonitoringController.GetSystemOverview (Gsdt.ModbusGateway)'
2025-05-26 11:34:02.727 +08:00 [ERR] Connection id "0HNCS0OKEJ1KF", Request id "0HNCS0OKEJ1KF:00000001": An unhandled exception was thrown by the application.
System.NotSupportedException: JsonTypeInfo metadata for type 'Gsdt.ModbusGateway.Controllers.SystemOverview' was not provided by TypeInfoResolver of type '[]'. If using source generation, ensure that all root types passed to the serializer have been annotated with 'JsonSerializableAttribute', along with any types that might be serialized polymorphically.
   at System.Text.Json.ThrowHelper.ThrowNotSupportedException_NoMetadataForType(Type type, IJsonTypeInfoResolver resolver)
   at System.Text.Json.JsonSerializerOptions.GetTypeInfoInternal(Type type, Boolean ensureConfigured, Nullable`1 ensureNotNull, Boolean resolveIfMutable, Boolean fallBackToNearestAncestorType)
   at System.Text.Json.JsonSerializerOptions.GetTypeInfo(Type type)
   at Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter.WriteResponseBodyAsync(OutputFormatterWriteContext context, Encoding selectedEncoding)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http.HttpProtocol.ProcessRequests[TContext](IHttpApplication`1 application)
2025-05-26 11:34:02.735 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/monitoring/overview - 500 0 null 55.8834ms
2025-05-26 11:34:14.469 +08:00 [INF] === Modbus性能报告 ===
2025-05-26 11:34:14.471 +08:00 [INF] 设备 1001: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:34:14.473 +08:00 [WRN] 设备 1001 最近错误: Health check failed
2025-05-26 11:34:14.474 +08:00 [INF] 设备 1002: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:34:14.475 +08:00 [WRN] 设备 1002 最近错误: Health check failed
2025-05-26 11:34:14.476 +08:00 [INF] 设备 1003: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:34:14.477 +08:00 [WRN] 设备 1003 最近错误: Health check failed
2025-05-26 11:34:14.478 +08:00 [INF] 设备 1004: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:34:14.479 +08:00 [WRN] 设备 1004 最近错误: Health check failed
2025-05-26 11:34:14.480 +08:00 [INF] 设备 1005: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:34:14.481 +08:00 [WRN] 设备 1005 最近错误: Health check failed
2025-05-26 11:34:14.482 +08:00 [INF] 设备 1006: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:34:14.484 +08:00 [WRN] 设备 1006 最近错误: Health check failed
2025-05-26 11:34:14.484 +08:00 [INF] 设备 1007: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:34:14.486 +08:00 [WRN] 设备 1007 最近错误: Health check failed
2025-05-26 11:34:14.486 +08:00 [INF] 设备 1008: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:34:14.488 +08:00 [WRN] 设备 1008 最近错误: Health check failed
2025-05-26 11:34:14.488 +08:00 [INF] 设备 1009: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:34:14.490 +08:00 [WRN] 设备 1009 最近错误: Health check failed
2025-05-26 11:34:14.490 +08:00 [INF] 设备 1010: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:34:14.492 +08:00 [WRN] 设备 1010 最近错误: Health check failed
2025-05-26 11:34:14.492 +08:00 [INF] 设备 2001: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:34:14.494 +08:00 [WRN] 设备 2001 最近错误: Health check failed
2025-05-26 11:34:14.494 +08:00 [INF] === 性能报告结束 ===
2025-05-26 11:34:19.780 +08:00 [DBG] 开始监控请求: RequestId=240e3d1c, DeviceId=1001, FunctionCode=0
2025-05-26 11:34:19.780 +08:00 [WRN] 设备 1001 健康检查失败，连续失败次数: 2
2025-05-26 11:34:19.783 +08:00 [DBG] 完成请求监控: RequestId=240e3d1c, Success=false, TotalTime=3.1635ms, ExecutionTime=3.1635ms
2025-05-26 11:34:19.783 +08:00 [WRN] 请求失败: RequestId=240e3d1c, DeviceId=1001, Error=Health check failed
2025-05-26 11:34:19.785 +08:00 [DBG] 开始监控请求: RequestId=ef86a59a, DeviceId=1002, FunctionCode=0
2025-05-26 11:34:19.785 +08:00 [WRN] 设备 1002 健康检查失败，连续失败次数: 2
2025-05-26 11:34:19.786 +08:00 [DBG] 完成请求监控: RequestId=ef86a59a, Success=false, TotalTime=1.2197ms, ExecutionTime=1.2197ms
2025-05-26 11:34:19.786 +08:00 [WRN] 请求失败: RequestId=ef86a59a, DeviceId=1002, Error=Health check failed
2025-05-26 11:34:19.787 +08:00 [DBG] 开始监控请求: RequestId=cb2c62c3, DeviceId=1003, FunctionCode=0
2025-05-26 11:34:19.787 +08:00 [WRN] 设备 1003 健康检查失败，连续失败次数: 2
2025-05-26 11:34:19.788 +08:00 [DBG] 完成请求监控: RequestId=cb2c62c3, Success=false, TotalTime=0.8765ms, ExecutionTime=0.8765ms
2025-05-26 11:34:19.788 +08:00 [WRN] 请求失败: RequestId=cb2c62c3, DeviceId=1003, Error=Health check failed
2025-05-26 11:34:19.789 +08:00 [DBG] 开始监控请求: RequestId=fbfa32c2, DeviceId=1004, FunctionCode=0
2025-05-26 11:34:19.789 +08:00 [WRN] 设备 1004 健康检查失败，连续失败次数: 2
2025-05-26 11:34:19.789 +08:00 [DBG] 完成请求监控: RequestId=fbfa32c2, Success=false, TotalTime=0.6634ms, ExecutionTime=0.6634ms
2025-05-26 11:34:19.789 +08:00 [WRN] 请求失败: RequestId=fbfa32c2, DeviceId=1004, Error=Health check failed
2025-05-26 11:34:19.790 +08:00 [DBG] 开始监控请求: RequestId=a45a0dbf, DeviceId=1005, FunctionCode=0
2025-05-26 11:34:19.790 +08:00 [WRN] 设备 1005 健康检查失败，连续失败次数: 2
2025-05-26 11:34:19.790 +08:00 [DBG] 完成请求监控: RequestId=a45a0dbf, Success=false, TotalTime=0.4648ms, ExecutionTime=0.4648ms
2025-05-26 11:34:19.791 +08:00 [WRN] 请求失败: RequestId=a45a0dbf, DeviceId=1005, Error=Health check failed
2025-05-26 11:34:19.791 +08:00 [DBG] 开始监控请求: RequestId=7cde1de3, DeviceId=1006, FunctionCode=0
2025-05-26 11:34:19.791 +08:00 [WRN] 设备 1006 健康检查失败，连续失败次数: 2
2025-05-26 11:34:19.792 +08:00 [DBG] 完成请求监控: RequestId=7cde1de3, Success=false, TotalTime=0.5323ms, ExecutionTime=0.5323ms
2025-05-26 11:34:19.792 +08:00 [WRN] 请求失败: RequestId=7cde1de3, DeviceId=1006, Error=Health check failed
2025-05-26 11:34:19.792 +08:00 [DBG] 开始监控请求: RequestId=9e3b1a58, DeviceId=1007, FunctionCode=0
2025-05-26 11:34:19.792 +08:00 [WRN] 设备 1007 健康检查失败，连续失败次数: 2
2025-05-26 11:34:19.793 +08:00 [DBG] 完成请求监控: RequestId=9e3b1a58, Success=false, TotalTime=0.4788ms, ExecutionTime=0.4788ms
2025-05-26 11:34:19.793 +08:00 [WRN] 请求失败: RequestId=9e3b1a58, DeviceId=1007, Error=Health check failed
2025-05-26 11:34:19.793 +08:00 [DBG] 开始监控请求: RequestId=f869d9ea, DeviceId=1008, FunctionCode=0
2025-05-26 11:34:19.793 +08:00 [WRN] 设备 1008 健康检查失败，连续失败次数: 2
2025-05-26 11:34:19.794 +08:00 [DBG] 完成请求监控: RequestId=f869d9ea, Success=false, TotalTime=0.53ms, ExecutionTime=0.53ms
2025-05-26 11:34:19.794 +08:00 [WRN] 请求失败: RequestId=f869d9ea, DeviceId=1008, Error=Health check failed
2025-05-26 11:34:19.795 +08:00 [DBG] 开始监控请求: RequestId=94c355f6, DeviceId=1009, FunctionCode=0
2025-05-26 11:34:19.795 +08:00 [WRN] 设备 1009 健康检查失败，连续失败次数: 2
2025-05-26 11:34:19.795 +08:00 [DBG] 完成请求监控: RequestId=94c355f6, Success=false, TotalTime=0.6918ms, ExecutionTime=0.6918ms
2025-05-26 11:34:19.796 +08:00 [WRN] 请求失败: RequestId=94c355f6, DeviceId=1009, Error=Health check failed
2025-05-26 11:34:19.796 +08:00 [DBG] 开始监控请求: RequestId=f61f8e49, DeviceId=1010, FunctionCode=0
2025-05-26 11:34:19.796 +08:00 [WRN] 设备 1010 健康检查失败，连续失败次数: 2
2025-05-26 11:34:19.797 +08:00 [DBG] 完成请求监控: RequestId=f61f8e49, Success=false, TotalTime=0.5541ms, ExecutionTime=0.5541ms
2025-05-26 11:34:19.797 +08:00 [WRN] 请求失败: RequestId=f61f8e49, DeviceId=1010, Error=Health check failed
2025-05-26 11:34:19.797 +08:00 [DBG] 开始监控请求: RequestId=d87e04f7, DeviceId=2001, FunctionCode=0
2025-05-26 11:34:19.797 +08:00 [DBG] 开始读取保持寄存器: SlaveId=255, StartAddress=0, Quantity=1
2025-05-26 11:34:25.001 +08:00 [DBG] 设备 2001 健康检查失败: The write timed out.
2025-05-26 11:34:25.001 +08:00 [WRN] 设备 2001 健康检查失败，连续失败次数: 2
2025-05-26 11:34:25.002 +08:00 [DBG] 完成请求监控: RequestId=d87e04f7, Success=false, TotalTime=5204.5846ms, ExecutionTime=5204.5846ms
2025-05-26 11:34:25.002 +08:00 [WRN] 请求失败: RequestId=d87e04f7, DeviceId=2001, Error=Health check failed
2025-05-26 11:34:40.149 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/monitoring/version - null null
2025-05-26 11:34:40.151 +08:00 [INF] Executing endpoint 'Gsdt.ModbusGateway.Controllers.MonitoringController.GetVersion (Gsdt.ModbusGateway)'
2025-05-26 11:34:40.152 +08:00 [INF] Route matched with {action = "GetVersion", controller = "Monitoring"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[System.Object] GetVersion() on controller Gsdt.ModbusGateway.Controllers.MonitoringController (Gsdt.ModbusGateway).
2025-05-26 11:34:40.153 +08:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType4`3[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-05-26 11:34:40.155 +08:00 [INF] Executed action Gsdt.ModbusGateway.Controllers.MonitoringController.GetVersion (Gsdt.ModbusGateway) in 1.4682ms
2025-05-26 11:34:40.155 +08:00 [INF] Executed endpoint 'Gsdt.ModbusGateway.Controllers.MonitoringController.GetVersion (Gsdt.ModbusGateway)'
2025-05-26 11:34:40.156 +08:00 [ERR] Connection id "0HNCS0OKEJ1KG", Request id "0HNCS0OKEJ1KG:00000001": An unhandled exception was thrown by the application.
System.NotSupportedException: JsonTypeInfo metadata for type '<>f__AnonymousType4`3[System.String,System.String,System.String[]]' was not provided by TypeInfoResolver of type '[]'. If using source generation, ensure that all root types passed to the serializer have been annotated with 'JsonSerializableAttribute', along with any types that might be serialized polymorphically.
   at System.Text.Json.ThrowHelper.ThrowNotSupportedException_NoMetadataForType(Type type, IJsonTypeInfoResolver resolver)
   at System.Text.Json.JsonSerializerOptions.GetTypeInfoInternal(Type type, Boolean ensureConfigured, Nullable`1 ensureNotNull, Boolean resolveIfMutable, Boolean fallBackToNearestAncestorType)
   at System.Text.Json.JsonSerializerOptions.GetTypeInfo(Type type)
   at Microsoft.AspNetCore.Mvc.Formatters.SystemTextJsonOutputFormatter.WriteResponseBodyAsync(OutputFormatterWriteContext context, Encoding selectedEncoding)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Http.HttpProtocol.ProcessRequests[TContext](IHttpApplication`1 application)
2025-05-26 11:34:40.159 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/monitoring/version - 500 0 null 10.297ms
2025-05-26 11:34:44.478 +08:00 [INF] === Modbus性能报告 ===
2025-05-26 11:34:44.479 +08:00 [INF] 设备 1001: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:34:44.480 +08:00 [WRN] 设备 1001 最近错误: Health check failed; Health check failed
2025-05-26 11:34:44.480 +08:00 [INF] 设备 1002: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:34:44.481 +08:00 [WRN] 设备 1002 最近错误: Health check failed; Health check failed
2025-05-26 11:34:44.482 +08:00 [INF] 设备 1003: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:34:44.483 +08:00 [WRN] 设备 1003 最近错误: Health check failed; Health check failed
2025-05-26 11:34:44.483 +08:00 [INF] 设备 1004: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:34:44.485 +08:00 [WRN] 设备 1004 最近错误: Health check failed; Health check failed
2025-05-26 11:34:44.486 +08:00 [INF] 设备 1005: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:34:44.487 +08:00 [WRN] 设备 1005 最近错误: Health check failed; Health check failed
2025-05-26 11:34:44.488 +08:00 [INF] 设备 1006: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:34:44.489 +08:00 [WRN] 设备 1006 最近错误: Health check failed; Health check failed
2025-05-26 11:34:44.490 +08:00 [INF] 设备 1007: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:34:44.491 +08:00 [WRN] 设备 1007 最近错误: Health check failed; Health check failed
2025-05-26 11:34:44.491 +08:00 [INF] 设备 1008: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:34:44.492 +08:00 [WRN] 设备 1008 最近错误: Health check failed; Health check failed
2025-05-26 11:34:44.493 +08:00 [INF] 设备 1009: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:34:44.494 +08:00 [WRN] 设备 1009 最近错误: Health check failed; Health check failed
2025-05-26 11:34:44.495 +08:00 [INF] 设备 1010: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:34:44.496 +08:00 [WRN] 设备 1010 最近错误: Health check failed; Health check failed
2025-05-26 11:34:44.496 +08:00 [INF] 设备 2001: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:34:44.497 +08:00 [WRN] 设备 2001 最近错误: Health check failed; Health check failed
2025-05-26 11:34:44.498 +08:00 [INF] === 性能报告结束 ===
2025-05-26 11:34:54.492 +08:00 [INF] Application is shutting down...
2025-05-26 11:34:54.496 +08:00 [INF] 连接健康检查服务正在停止
2025-05-26 11:34:54.497 +08:00 [INF] 正在停止Modbus TCP网关服务...
2025-05-26 11:34:54.498 +08:00 [INF] Modbus TCP网关服务已停止
2025-05-26 11:34:54.503 +08:00 [DBG] 正在释放线程安全ModbusClient连接池资源
2025-05-26 11:34:54.503 +08:00 [DBG] 线程安全ModbusClient连接池资源已释放
2025-05-26 11:34:54.503 +08:00 [INF] 正在输出最终性能报告...
2025-05-26 11:34:54.504 +08:00 [INF] === Modbus性能报告 ===
2025-05-26 11:34:54.505 +08:00 [INF] 设备 1001: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:34:54.506 +08:00 [WRN] 设备 1001 最近错误: Health check failed; Health check failed
2025-05-26 11:34:54.507 +08:00 [INF] 设备 1002: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:34:54.508 +08:00 [WRN] 设备 1002 最近错误: Health check failed; Health check failed
2025-05-26 11:34:54.509 +08:00 [INF] 设备 1003: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:34:54.510 +08:00 [WRN] 设备 1003 最近错误: Health check failed; Health check failed
2025-05-26 11:34:54.511 +08:00 [INF] 设备 1004: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:34:54.512 +08:00 [WRN] 设备 1004 最近错误: Health check failed; Health check failed
2025-05-26 11:34:54.513 +08:00 [INF] 设备 1005: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:34:54.514 +08:00 [WRN] 设备 1005 最近错误: Health check failed; Health check failed
2025-05-26 11:34:54.515 +08:00 [INF] 设备 1006: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:34:54.516 +08:00 [WRN] 设备 1006 最近错误: Health check failed; Health check failed
2025-05-26 11:34:54.517 +08:00 [INF] 设备 1007: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:34:54.518 +08:00 [WRN] 设备 1007 最近错误: Health check failed; Health check failed
2025-05-26 11:34:54.519 +08:00 [INF] 设备 1008: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:34:54.520 +08:00 [WRN] 设备 1008 最近错误: Health check failed; Health check failed
2025-05-26 11:34:54.521 +08:00 [INF] 设备 1009: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:34:54.522 +08:00 [WRN] 设备 1009 最近错误: Health check failed; Health check failed
2025-05-26 11:34:54.522 +08:00 [INF] 设备 1010: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:34:54.524 +08:00 [WRN] 设备 1010 最近错误: Health check failed; Health check failed
2025-05-26 11:34:54.524 +08:00 [INF] 设备 2001: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:34:54.525 +08:00 [WRN] 设备 2001 最近错误: Health check failed; Health check failed
2025-05-26 11:34:54.526 +08:00 [INF] === 性能报告结束 ===
2025-05-26 11:35:44.155 +08:00 [INF] 正在验证配置...
2025-05-26 11:35:44.197 +08:00 [INF] 配置验证通过
2025-05-26 11:35:44.354 +08:00 [INF] 正在启动Modbus TCP网关服务...
2025-05-26 11:35:44.356 +08:00 [INF] 创建Modbus客户端：ID=1001, IP=***************, Port=8080
2025-05-26 11:35:44.369 +08:00 [DBG] 已注册设备 1001 的线程安全客户端包装器
2025-05-26 11:35:44.369 +08:00 [INF] 已注册设备 1001 到线程安全连接池
2025-05-26 11:35:44.370 +08:00 [INF] 创建Modbus客户端：ID=1002, IP=***************, Port=8080
2025-05-26 11:35:44.370 +08:00 [DBG] 已注册设备 1002 的线程安全客户端包装器
2025-05-26 11:35:44.370 +08:00 [INF] 已注册设备 1002 到线程安全连接池
2025-05-26 11:35:44.371 +08:00 [INF] 创建Modbus客户端：ID=1003, IP=***************, Port=8080
2025-05-26 11:35:44.371 +08:00 [DBG] 已注册设备 1003 的线程安全客户端包装器
2025-05-26 11:35:44.371 +08:00 [INF] 已注册设备 1003 到线程安全连接池
2025-05-26 11:35:44.372 +08:00 [INF] 创建Modbus客户端：ID=1004, IP=***************, Port=8080
2025-05-26 11:35:44.373 +08:00 [DBG] 已注册设备 1004 的线程安全客户端包装器
2025-05-26 11:35:44.373 +08:00 [INF] 已注册设备 1004 到线程安全连接池
2025-05-26 11:35:44.373 +08:00 [INF] 创建Modbus客户端：ID=1005, IP=***************, Port=8080
2025-05-26 11:35:44.374 +08:00 [DBG] 已注册设备 1005 的线程安全客户端包装器
2025-05-26 11:35:44.374 +08:00 [INF] 已注册设备 1005 到线程安全连接池
2025-05-26 11:35:44.375 +08:00 [INF] 创建Modbus客户端：ID=1006, IP=***************, Port=8080
2025-05-26 11:35:44.375 +08:00 [DBG] 已注册设备 1006 的线程安全客户端包装器
2025-05-26 11:35:44.375 +08:00 [INF] 已注册设备 1006 到线程安全连接池
2025-05-26 11:35:44.376 +08:00 [INF] 创建Modbus客户端：ID=1007, IP=***************, Port=8080
2025-05-26 11:35:44.377 +08:00 [DBG] 已注册设备 1007 的线程安全客户端包装器
2025-05-26 11:35:44.377 +08:00 [INF] 已注册设备 1007 到线程安全连接池
2025-05-26 11:35:44.377 +08:00 [INF] 创建Modbus客户端：ID=1008, IP=***************, Port=8080
2025-05-26 11:35:44.378 +08:00 [DBG] 已注册设备 1008 的线程安全客户端包装器
2025-05-26 11:35:44.378 +08:00 [INF] 已注册设备 1008 到线程安全连接池
2025-05-26 11:35:44.378 +08:00 [INF] 创建Modbus客户端：ID=1009, IP=***************, Port=8080
2025-05-26 11:35:44.379 +08:00 [DBG] 已注册设备 1009 的线程安全客户端包装器
2025-05-26 11:35:44.379 +08:00 [INF] 已注册设备 1009 到线程安全连接池
2025-05-26 11:35:44.380 +08:00 [INF] 创建Modbus客户端：ID=1010, IP=***************, Port=8080
2025-05-26 11:35:44.380 +08:00 [DBG] 已注册设备 1010 的线程安全客户端包装器
2025-05-26 11:35:44.380 +08:00 [INF] 已注册设备 1010 到线程安全连接池
2025-05-26 11:35:44.381 +08:00 [INF] 创建Modbus客户端并连接到设备：ID=2001, PortName=COM3, BaudRate=9600
2025-05-26 11:35:44.399 +08:00 [DBG] 已注册设备 2001 的线程安全客户端包装器
2025-05-26 11:35:44.399 +08:00 [INF] 已注册设备 2001 到线程安全连接池
2025-05-26 11:35:44.405 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8080
2025-05-26 11:35:44.405 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8081
2025-05-26 11:35:44.406 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8082
2025-05-26 11:35:44.406 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8083
2025-05-26 11:35:44.407 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8084
2025-05-26 11:35:44.408 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8085
2025-05-26 11:35:44.408 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8086
2025-05-26 11:35:44.409 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8087
2025-05-26 11:35:44.409 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8088
2025-05-26 11:35:44.410 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8089
2025-05-26 11:35:44.411 +08:00 [INF] Modbus连接健康检查服务已启动
2025-05-26 11:35:44.415 +08:00 [DBG] 开始监控请求: RequestId=afec7470, DeviceId=1001, FunctionCode=0
2025-05-26 11:35:44.416 +08:00 [WRN] 设备 1001 健康检查失败，连续失败次数: 1
2025-05-26 11:35:44.417 +08:00 [DBG] 完成请求监控: RequestId=afec7470, Success=false, TotalTime=3.641ms, ExecutionTime=3.641ms
2025-05-26 11:35:44.419 +08:00 [WRN] 请求失败: RequestId=afec7470, DeviceId=1001, Error=Health check failed
2025-05-26 11:35:44.420 +08:00 [DBG] 开始监控请求: RequestId=73a4f16f, DeviceId=1002, FunctionCode=0
2025-05-26 11:35:44.420 +08:00 [WRN] 设备 1002 健康检查失败，连续失败次数: 1
2025-05-26 11:35:44.420 +08:00 [DBG] 完成请求监控: RequestId=73a4f16f, Success=false, TotalTime=0.5911ms, ExecutionTime=0.5911ms
2025-05-26 11:35:44.420 +08:00 [WRN] 请求失败: RequestId=73a4f16f, DeviceId=1002, Error=Health check failed
2025-05-26 11:35:44.421 +08:00 [DBG] 开始监控请求: RequestId=11d5d89c, DeviceId=1003, FunctionCode=0
2025-05-26 11:35:44.421 +08:00 [WRN] 设备 1003 健康检查失败，连续失败次数: 1
2025-05-26 11:35:44.422 +08:00 [DBG] 完成请求监控: RequestId=11d5d89c, Success=false, TotalTime=0.639ms, ExecutionTime=0.639ms
2025-05-26 11:35:44.422 +08:00 [WRN] 请求失败: RequestId=11d5d89c, DeviceId=1003, Error=Health check failed
2025-05-26 11:35:44.423 +08:00 [DBG] 开始监控请求: RequestId=32600d3b, DeviceId=1004, FunctionCode=0
2025-05-26 11:35:44.423 +08:00 [WRN] 设备 1004 健康检查失败，连续失败次数: 1
2025-05-26 11:35:44.423 +08:00 [DBG] 完成请求监控: RequestId=32600d3b, Success=false, TotalTime=0.5821ms, ExecutionTime=0.5821ms
2025-05-26 11:35:44.423 +08:00 [WRN] 请求失败: RequestId=32600d3b, DeviceId=1004, Error=Health check failed
2025-05-26 11:35:44.424 +08:00 [DBG] 开始监控请求: RequestId=b52c8234, DeviceId=1005, FunctionCode=0
2025-05-26 11:35:44.424 +08:00 [WRN] 设备 1005 健康检查失败，连续失败次数: 1
2025-05-26 11:35:44.424 +08:00 [DBG] 完成请求监控: RequestId=b52c8234, Success=false, TotalTime=0.5556ms, ExecutionTime=0.5556ms
2025-05-26 11:35:44.424 +08:00 [WRN] 请求失败: RequestId=b52c8234, DeviceId=1005, Error=Health check failed
2025-05-26 11:35:44.425 +08:00 [DBG] 开始监控请求: RequestId=44b51269, DeviceId=1006, FunctionCode=0
2025-05-26 11:35:44.425 +08:00 [WRN] 设备 1006 健康检查失败，连续失败次数: 1
2025-05-26 11:35:44.426 +08:00 [DBG] 完成请求监控: RequestId=44b51269, Success=false, TotalTime=0.589ms, ExecutionTime=0.589ms
2025-05-26 11:35:44.426 +08:00 [WRN] 请求失败: RequestId=44b51269, DeviceId=1006, Error=Health check failed
2025-05-26 11:35:44.427 +08:00 [DBG] 开始监控请求: RequestId=0634ad67, DeviceId=1007, FunctionCode=0
2025-05-26 11:35:44.427 +08:00 [WRN] 设备 1007 健康检查失败，连续失败次数: 1
2025-05-26 11:35:44.428 +08:00 [DBG] 完成请求监控: RequestId=0634ad67, Success=false, TotalTime=0.7648ms, ExecutionTime=0.7648ms
2025-05-26 11:35:44.428 +08:00 [WRN] 请求失败: RequestId=0634ad67, DeviceId=1007, Error=Health check failed
2025-05-26 11:35:44.429 +08:00 [DBG] 开始监控请求: RequestId=b35ee1b1, DeviceId=1008, FunctionCode=0
2025-05-26 11:35:44.429 +08:00 [WRN] 设备 1008 健康检查失败，连续失败次数: 1
2025-05-26 11:35:44.429 +08:00 [DBG] 完成请求监控: RequestId=b35ee1b1, Success=false, TotalTime=0.651ms, ExecutionTime=0.651ms
2025-05-26 11:35:44.429 +08:00 [WRN] 请求失败: RequestId=b35ee1b1, DeviceId=1008, Error=Health check failed
2025-05-26 11:35:44.430 +08:00 [DBG] 开始监控请求: RequestId=9b550769, DeviceId=1009, FunctionCode=0
2025-05-26 11:35:44.430 +08:00 [WRN] 设备 1009 健康检查失败，连续失败次数: 1
2025-05-26 11:35:44.431 +08:00 [DBG] 完成请求监控: RequestId=9b550769, Success=false, TotalTime=0.5778ms, ExecutionTime=0.5778ms
2025-05-26 11:35:44.431 +08:00 [WRN] 请求失败: RequestId=9b550769, DeviceId=1009, Error=Health check failed
2025-05-26 11:35:44.432 +08:00 [DBG] 开始监控请求: RequestId=f6b26971, DeviceId=1010, FunctionCode=0
2025-05-26 11:35:44.432 +08:00 [WRN] 设备 1010 健康检查失败，连续失败次数: 1
2025-05-26 11:35:44.432 +08:00 [DBG] 完成请求监控: RequestId=f6b26971, Success=false, TotalTime=0.5461ms, ExecutionTime=0.5461ms
2025-05-26 11:35:44.432 +08:00 [WRN] 请求失败: RequestId=f6b26971, DeviceId=1010, Error=Health check failed
2025-05-26 11:35:44.433 +08:00 [DBG] 开始监控请求: RequestId=52e99f4a, DeviceId=2001, FunctionCode=0
2025-05-26 11:35:44.434 +08:00 [DBG] 开始读取保持寄存器: SlaveId=255, StartAddress=0, Quantity=1
2025-05-26 11:35:44.512 +08:00 [INF] Now listening on: http://localhost:5000
2025-05-26 11:35:44.513 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-05-26 11:35:44.513 +08:00 [INF] Hosting environment: Production
2025-05-26 11:35:44.514 +08:00 [INF] Content root path: C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway
2025-05-26 11:35:46.489 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/ - null null
2025-05-26 11:35:46.532 +08:00 [INF] Executing endpoint 'HTTP: GET /'
2025-05-26 11:35:46.559 +08:00 [INF] Executed endpoint 'HTTP: GET /'
2025-05-26 11:35:46.566 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/ - 200 null text/plain; charset=utf-8 78.1504ms
2025-05-26 11:35:47.300 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/favicon.ico - null null
2025-05-26 11:35:47.304 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/favicon.ico - 404 0 null 3.7906ms
2025-05-26 11:35:47.306 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/favicon.ico, Response status code: 404
2025-05-26 11:35:49.804 +08:00 [DBG] 设备 2001 健康检查失败: The write timed out.
2025-05-26 11:35:49.804 +08:00 [WRN] 设备 2001 健康检查失败，连续失败次数: 1
2025-05-26 11:35:49.805 +08:00 [DBG] 完成请求监控: RequestId=52e99f4a, Success=false, TotalTime=5371.6087ms, ExecutionTime=5371.6087ms
2025-05-26 11:35:49.805 +08:00 [WRN] 请求失败: RequestId=52e99f4a, DeviceId=2001, Error=Health check failed
2025-05-26 11:36:14.363 +08:00 [INF] === Modbus性能报告 ===
2025-05-26 11:36:14.366 +08:00 [INF] 设备 1001: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:36:14.369 +08:00 [WRN] 设备 1001 最近错误: Health check failed
2025-05-26 11:36:14.369 +08:00 [INF] 设备 1002: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:36:14.371 +08:00 [WRN] 设备 1002 最近错误: Health check failed
2025-05-26 11:36:14.372 +08:00 [INF] 设备 1003: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:36:14.374 +08:00 [WRN] 设备 1003 最近错误: Health check failed
2025-05-26 11:36:14.374 +08:00 [INF] 设备 1004: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:36:14.376 +08:00 [WRN] 设备 1004 最近错误: Health check failed
2025-05-26 11:36:14.376 +08:00 [INF] 设备 1005: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:36:14.378 +08:00 [WRN] 设备 1005 最近错误: Health check failed
2025-05-26 11:36:14.378 +08:00 [INF] 设备 1006: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:36:14.379 +08:00 [WRN] 设备 1006 最近错误: Health check failed
2025-05-26 11:36:14.380 +08:00 [INF] 设备 1007: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:36:14.381 +08:00 [WRN] 设备 1007 最近错误: Health check failed
2025-05-26 11:36:14.382 +08:00 [INF] 设备 1008: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:36:14.383 +08:00 [WRN] 设备 1008 最近错误: Health check failed
2025-05-26 11:36:14.384 +08:00 [INF] 设备 1009: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:36:14.386 +08:00 [WRN] 设备 1009 最近错误: Health check failed
2025-05-26 11:36:14.387 +08:00 [INF] 设备 1010: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:36:14.388 +08:00 [WRN] 设备 1010 最近错误: Health check failed
2025-05-26 11:36:14.389 +08:00 [INF] 设备 2001: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:36:14.390 +08:00 [WRN] 设备 2001 最近错误: Health check failed
2025-05-26 11:36:14.390 +08:00 [INF] === 性能报告结束 ===
2025-05-26 11:36:19.816 +08:00 [DBG] 开始监控请求: RequestId=eab667fc, DeviceId=1001, FunctionCode=0
2025-05-26 11:36:19.816 +08:00 [WRN] 设备 1001 健康检查失败，连续失败次数: 2
2025-05-26 11:36:19.817 +08:00 [DBG] 完成请求监控: RequestId=eab667fc, Success=false, TotalTime=1.2387ms, ExecutionTime=1.2387ms
2025-05-26 11:36:19.818 +08:00 [WRN] 请求失败: RequestId=eab667fc, DeviceId=1001, Error=Health check failed
2025-05-26 11:36:19.818 +08:00 [DBG] 开始监控请求: RequestId=30ff1d60, DeviceId=1002, FunctionCode=0
2025-05-26 11:36:19.818 +08:00 [WRN] 设备 1002 健康检查失败，连续失败次数: 2
2025-05-26 11:36:19.819 +08:00 [DBG] 完成请求监控: RequestId=30ff1d60, Success=false, TotalTime=0.6373ms, ExecutionTime=0.6373ms
2025-05-26 11:36:19.819 +08:00 [WRN] 请求失败: RequestId=30ff1d60, DeviceId=1002, Error=Health check failed
2025-05-26 11:36:19.820 +08:00 [DBG] 开始监控请求: RequestId=c06a0f7a, DeviceId=1003, FunctionCode=0
2025-05-26 11:36:19.820 +08:00 [WRN] 设备 1003 健康检查失败，连续失败次数: 2
2025-05-26 11:36:19.820 +08:00 [DBG] 完成请求监控: RequestId=c06a0f7a, Success=false, TotalTime=0.6089ms, ExecutionTime=0.6089ms
2025-05-26 11:36:19.820 +08:00 [WRN] 请求失败: RequestId=c06a0f7a, DeviceId=1003, Error=Health check failed
2025-05-26 11:36:19.821 +08:00 [DBG] 开始监控请求: RequestId=54517781, DeviceId=1004, FunctionCode=0
2025-05-26 11:36:19.821 +08:00 [WRN] 设备 1004 健康检查失败，连续失败次数: 2
2025-05-26 11:36:19.821 +08:00 [DBG] 完成请求监控: RequestId=54517781, Success=false, TotalTime=0.5423ms, ExecutionTime=0.5423ms
2025-05-26 11:36:19.821 +08:00 [WRN] 请求失败: RequestId=54517781, DeviceId=1004, Error=Health check failed
2025-05-26 11:36:19.822 +08:00 [DBG] 开始监控请求: RequestId=fe1dc82c, DeviceId=1005, FunctionCode=0
2025-05-26 11:36:19.822 +08:00 [WRN] 设备 1005 健康检查失败，连续失败次数: 2
2025-05-26 11:36:19.823 +08:00 [DBG] 完成请求监控: RequestId=fe1dc82c, Success=false, TotalTime=0.5598ms, ExecutionTime=0.5598ms
2025-05-26 11:36:19.823 +08:00 [WRN] 请求失败: RequestId=fe1dc82c, DeviceId=1005, Error=Health check failed
2025-05-26 11:36:19.824 +08:00 [DBG] 开始监控请求: RequestId=1cb70fa3, DeviceId=1006, FunctionCode=0
2025-05-26 11:36:19.824 +08:00 [WRN] 设备 1006 健康检查失败，连续失败次数: 2
2025-05-26 11:36:19.824 +08:00 [DBG] 完成请求监控: RequestId=1cb70fa3, Success=false, TotalTime=0.7887ms, ExecutionTime=0.7887ms
2025-05-26 11:36:19.824 +08:00 [WRN] 请求失败: RequestId=1cb70fa3, DeviceId=1006, Error=Health check failed
2025-05-26 11:36:19.825 +08:00 [DBG] 开始监控请求: RequestId=0909c4fa, DeviceId=1007, FunctionCode=0
2025-05-26 11:36:19.825 +08:00 [WRN] 设备 1007 健康检查失败，连续失败次数: 2
2025-05-26 11:36:19.826 +08:00 [DBG] 完成请求监控: RequestId=0909c4fa, Success=false, TotalTime=0.9702ms, ExecutionTime=0.9702ms
2025-05-26 11:36:19.826 +08:00 [WRN] 请求失败: RequestId=0909c4fa, DeviceId=1007, Error=Health check failed
2025-05-26 11:36:19.827 +08:00 [DBG] 开始监控请求: RequestId=b3052124, DeviceId=1008, FunctionCode=0
2025-05-26 11:36:19.827 +08:00 [WRN] 设备 1008 健康检查失败，连续失败次数: 2
2025-05-26 11:36:19.828 +08:00 [DBG] 完成请求监控: RequestId=b3052124, Success=false, TotalTime=0.6683ms, ExecutionTime=0.6683ms
2025-05-26 11:36:19.828 +08:00 [WRN] 请求失败: RequestId=b3052124, DeviceId=1008, Error=Health check failed
2025-05-26 11:36:19.828 +08:00 [DBG] 开始监控请求: RequestId=a433ebe1, DeviceId=1009, FunctionCode=0
2025-05-26 11:36:19.828 +08:00 [WRN] 设备 1009 健康检查失败，连续失败次数: 2
2025-05-26 11:36:19.829 +08:00 [DBG] 完成请求监控: RequestId=a433ebe1, Success=false, TotalTime=0.5406ms, ExecutionTime=0.5406ms
2025-05-26 11:36:19.829 +08:00 [WRN] 请求失败: RequestId=a433ebe1, DeviceId=1009, Error=Health check failed
2025-05-26 11:36:19.829 +08:00 [DBG] 开始监控请求: RequestId=5caeb475, DeviceId=1010, FunctionCode=0
2025-05-26 11:36:19.829 +08:00 [WRN] 设备 1010 健康检查失败，连续失败次数: 2
2025-05-26 11:36:19.830 +08:00 [DBG] 完成请求监控: RequestId=5caeb475, Success=false, TotalTime=0.5459ms, ExecutionTime=0.5459ms
2025-05-26 11:36:19.830 +08:00 [WRN] 请求失败: RequestId=5caeb475, DeviceId=1010, Error=Health check failed
2025-05-26 11:36:19.831 +08:00 [DBG] 开始监控请求: RequestId=037a313f, DeviceId=2001, FunctionCode=0
2025-05-26 11:36:19.831 +08:00 [DBG] 开始读取保持寄存器: SlaveId=255, StartAddress=0, Quantity=1
2025-05-26 11:36:25.100 +08:00 [DBG] 设备 2001 健康检查失败: The write timed out.
2025-05-26 11:36:25.100 +08:00 [WRN] 设备 2001 健康检查失败，连续失败次数: 2
2025-05-26 11:36:25.101 +08:00 [DBG] 完成请求监控: RequestId=037a313f, Success=false, TotalTime=5269.8135ms, ExecutionTime=5269.8135ms
2025-05-26 11:36:25.101 +08:00 [WRN] 请求失败: RequestId=037a313f, DeviceId=2001, Error=Health check failed
2025-05-26 11:36:30.580 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/Monitoring/overview - null null
2025-05-26 11:36:30.585 +08:00 [INF] Executing endpoint 'Gsdt.ModbusGateway.Controllers.MonitoringController.GetSystemOverview (Gsdt.ModbusGateway)'
2025-05-26 11:36:30.603 +08:00 [INF] Route matched with {action = "GetSystemOverview", controller = "Monitoring"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[Gsdt.ModbusGateway.Controllers.SystemOverview] GetSystemOverview() on controller Gsdt.ModbusGateway.Controllers.MonitoringController (Gsdt.ModbusGateway).
2025-05-26 11:36:30.612 +08:00 [INF] Executing action method Gsdt.ModbusGateway.Controllers.MonitoringController.GetSystemOverview (Gsdt.ModbusGateway) - Validation state: "Valid"
2025-05-26 11:36:55.494 +08:00 [DBG] 开始监控请求: RequestId=7c19bdb5, DeviceId=1001, FunctionCode=0
2025-05-26 11:36:55.495 +08:00 [WRN] 设备 1001 健康检查失败，连续失败次数: 3
2025-05-26 11:36:55.526 +08:00 [DBG] 完成请求监控: RequestId=7c19bdb5, Success=false, TotalTime=32.5806ms, ExecutionTime=32.5806ms
2025-05-26 11:36:55.501 +08:00 [INF] === Modbus性能报告 ===
2025-05-26 11:36:55.526 +08:00 [WRN] 请求失败: RequestId=7c19bdb5, DeviceId=1001, Error=Health check failed
2025-05-26 11:36:55.527 +08:00 [INF] 设备 1001: 总请求=3, 成功=0, 失败=2, 活跃请求=1, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:36:55.545 +08:00 [INF] 尝试重连设备 1001
2025-05-26 11:36:55.579 +08:00 [DBG] 开始监控请求: RequestId=b0b7284f, DeviceId=1002, FunctionCode=0
2025-05-26 11:36:55.577 +08:00 [WRN] 设备 1001 最近错误: Health check failed; Health check failed
2025-05-26 11:36:55.579 +08:00 [WRN] 设备 1002 健康检查失败，连续失败次数: 3
2025-05-26 11:36:55.579 +08:00 [DBG] 完成请求监控: RequestId=b0b7284f, Success=false, TotalTime=0.6462ms, ExecutionTime=0.6462ms
2025-05-26 11:36:55.579 +08:00 [INF] 设备 1002: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:36:55.579 +08:00 [WRN] 请求失败: RequestId=b0b7284f, DeviceId=1002, Error=Health check failed
2025-05-26 11:36:55.581 +08:00 [WRN] 设备 1002 最近错误: Health check failed; Health check failed
2025-05-26 11:36:55.592 +08:00 [INF] 尝试重连设备 1002
2025-05-26 11:36:55.603 +08:00 [DBG] 开始监控请求: RequestId=877e947f, DeviceId=1003, FunctionCode=0
2025-05-26 11:36:55.593 +08:00 [INF] 设备 1003: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:36:55.604 +08:00 [WRN] 设备 1003 健康检查失败，连续失败次数: 3
2025-05-26 11:36:55.616 +08:00 [DBG] 完成请求监控: RequestId=877e947f, Success=false, TotalTime=12.2265ms, ExecutionTime=12.2265ms
2025-05-26 11:36:55.610 +08:00 [WRN] 设备 1003 最近错误: Health check failed; Health check failed
2025-05-26 11:36:55.616 +08:00 [WRN] 请求失败: RequestId=877e947f, DeviceId=1003, Error=Health check failed
2025-05-26 11:36:55.617 +08:00 [INF] 设备 1004: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:36:55.628 +08:00 [INF] 尝试重连设备 1003
2025-05-26 11:36:55.641 +08:00 [DBG] 开始监控请求: RequestId=98d52d67, DeviceId=1004, FunctionCode=0
2025-05-26 11:36:55.640 +08:00 [WRN] 设备 1004 最近错误: Health check failed; Health check failed
2025-05-26 11:36:55.641 +08:00 [WRN] 设备 1004 健康检查失败，连续失败次数: 3
2025-05-26 11:36:55.652 +08:00 [DBG] 完成请求监控: RequestId=98d52d67, Success=false, TotalTime=11.376ms, ExecutionTime=11.376ms
2025-05-26 11:36:55.646 +08:00 [INF] 设备 1005: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:36:55.652 +08:00 [WRN] 请求失败: RequestId=98d52d67, DeviceId=1004, Error=Health check failed
2025-05-26 11:36:55.663 +08:00 [WRN] 设备 1005 最近错误: Health check failed; Health check failed
2025-05-26 11:36:55.669 +08:00 [INF] 尝试重连设备 1004
2025-05-26 11:37:02.753 +08:00 [DBG] 开始监控请求: RequestId=6146c809, DeviceId=1005, FunctionCode=0
2025-05-26 11:37:02.747 +08:00 [INF] 设备 1006: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:37:02.753 +08:00 [WRN] 设备 1005 健康检查失败，连续失败次数: 3
2025-05-26 11:37:02.758 +08:00 [DBG] 完成请求监控: RequestId=6146c809, Success=false, TotalTime=4.6476ms, ExecutionTime=4.6476ms
2025-05-26 11:37:02.757 +08:00 [WRN] 设备 1006 最近错误: Health check failed; Health check failed
2025-05-26 11:37:02.758 +08:00 [INF] Executed action method Gsdt.ModbusGateway.Controllers.MonitoringController.GetSystemOverview (Gsdt.ModbusGateway), returned result Microsoft.AspNetCore.Mvc.OkObjectResult in 32131.0275ms.
2025-05-26 11:37:02.758 +08:00 [WRN] 请求失败: RequestId=6146c809, DeviceId=1005, Error=Health check failed
2025-05-26 11:37:02.758 +08:00 [INF] 设备 1007: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:37:02.760 +08:00 [INF] 尝试重连设备 1005
2025-05-26 11:37:02.762 +08:00 [DBG] 开始监控请求: RequestId=5f9506df, DeviceId=1006, FunctionCode=0
2025-05-26 11:37:02.761 +08:00 [WRN] 设备 1007 最近错误: Health check failed; Health check failed
2025-05-26 11:37:02.762 +08:00 [WRN] 设备 1006 健康检查失败，连续失败次数: 3
2025-05-26 11:37:02.763 +08:00 [DBG] 完成请求监控: RequestId=5f9506df, Success=false, TotalTime=1.1417ms, ExecutionTime=1.1417ms
2025-05-26 11:37:02.762 +08:00 [INF] 设备 1008: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:37:02.763 +08:00 [WRN] 请求失败: RequestId=5f9506df, DeviceId=1006, Error=Health check failed
2025-05-26 11:37:02.764 +08:00 [WRN] 设备 1008 最近错误: Health check failed; Health check failed
2025-05-26 11:37:02.765 +08:00 [INF] 尝试重连设备 1006
2025-05-26 11:37:02.766 +08:00 [DBG] 开始监控请求: RequestId=c489fb8c, DeviceId=1007, FunctionCode=0
2025-05-26 11:37:02.766 +08:00 [INF] 设备 1009: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:37:02.766 +08:00 [WRN] 设备 1007 健康检查失败，连续失败次数: 3
2025-05-26 11:37:02.768 +08:00 [DBG] 完成请求监控: RequestId=c489fb8c, Success=false, TotalTime=1.9777ms, ExecutionTime=1.9777ms
2025-05-26 11:37:02.768 +08:00 [WRN] 设备 1009 最近错误: Health check failed; Health check failed
2025-05-26 11:37:02.768 +08:00 [WRN] 请求失败: RequestId=c489fb8c, DeviceId=1007, Error=Health check failed
2025-05-26 11:37:02.769 +08:00 [INF] 设备 1010: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:37:02.769 +08:00 [INF] 尝试重连设备 1007
2025-05-26 11:37:02.771 +08:00 [DBG] 开始监控请求: RequestId=31a8205f, DeviceId=1008, FunctionCode=0
2025-05-26 11:37:02.770 +08:00 [INF] Executing OkObjectResult, writing value of type 'Gsdt.ModbusGateway.Controllers.SystemOverview'.
2025-05-26 11:37:02.771 +08:00 [WRN] 设备 1010 最近错误: Health check failed; Health check failed
2025-05-26 11:37:02.772 +08:00 [WRN] 设备 1008 健康检查失败，连续失败次数: 3
2025-05-26 11:37:02.773 +08:00 [DBG] 完成请求监控: RequestId=31a8205f, Success=false, TotalTime=1.6789ms, ExecutionTime=1.6789ms
2025-05-26 11:37:02.773 +08:00 [INF] 设备 2001: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:37:02.773 +08:00 [WRN] 请求失败: RequestId=31a8205f, DeviceId=1008, Error=Health check failed
2025-05-26 11:37:02.775 +08:00 [WRN] 设备 2001 最近错误: Health check failed; Health check failed
2025-05-26 11:37:02.775 +08:00 [INF] 尝试重连设备 1008
2025-05-26 11:37:02.777 +08:00 [DBG] 开始监控请求: RequestId=050e41ae, DeviceId=1009, FunctionCode=0
2025-05-26 11:37:02.776 +08:00 [INF] === 性能报告结束 ===
2025-05-26 11:37:02.777 +08:00 [WRN] 设备 1009 健康检查失败，连续失败次数: 3
2025-05-26 11:37:02.778 +08:00 [DBG] 完成请求监控: RequestId=050e41ae, Success=false, TotalTime=0.9869ms, ExecutionTime=0.9869ms
2025-05-26 11:37:02.778 +08:00 [WRN] 请求失败: RequestId=050e41ae, DeviceId=1009, Error=Health check failed
2025-05-26 11:37:02.779 +08:00 [INF] 尝试重连设备 1009
2025-05-26 11:37:02.779 +08:00 [DBG] 开始监控请求: RequestId=fd1615a3, DeviceId=1010, FunctionCode=0
2025-05-26 11:37:02.779 +08:00 [WRN] 设备 1010 健康检查失败，连续失败次数: 3
2025-05-26 11:37:02.780 +08:00 [DBG] 完成请求监控: RequestId=fd1615a3, Success=false, TotalTime=0.5841ms, ExecutionTime=0.5841ms
2025-05-26 11:37:02.780 +08:00 [WRN] 请求失败: RequestId=fd1615a3, DeviceId=1010, Error=Health check failed
2025-05-26 11:37:02.781 +08:00 [INF] 尝试重连设备 1010
2025-05-26 11:37:02.781 +08:00 [DBG] 开始监控请求: RequestId=4053e345, DeviceId=2001, FunctionCode=0
2025-05-26 11:37:02.781 +08:00 [DBG] 开始读取保持寄存器: SlaveId=255, StartAddress=0, Quantity=1
2025-05-26 11:37:02.849 +08:00 [INF] Executed action Gsdt.ModbusGateway.Controllers.MonitoringController.GetSystemOverview (Gsdt.ModbusGateway) in 32242.3367ms
2025-05-26 11:37:02.851 +08:00 [INF] Executed endpoint 'Gsdt.ModbusGateway.Controllers.MonitoringController.GetSystemOverview (Gsdt.ModbusGateway)'
2025-05-26 11:37:02.851 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/Monitoring/overview - 200 null application/json; charset=utf-8 32271.3412ms
2025-05-26 11:37:08.045 +08:00 [DBG] 设备 2001 健康检查失败: The write timed out.
2025-05-26 11:37:08.045 +08:00 [WRN] 设备 2001 健康检查失败，连续失败次数: 3
2025-05-26 11:37:08.046 +08:00 [DBG] 完成请求监控: RequestId=4053e345, Success=false, TotalTime=5264.2368ms, ExecutionTime=5264.2368ms
2025-05-26 11:37:08.046 +08:00 [WRN] 请求失败: RequestId=4053e345, DeviceId=2001, Error=Health check failed
2025-05-26 11:37:08.046 +08:00 [INF] 尝试重连设备 2001
2025-05-26 11:37:09.069 +08:00 [INF] 成功重连RTU设备 2001 (COM3)
2025-05-26 11:37:14.351 +08:00 [INF] === Modbus性能报告 ===
2025-05-26 11:37:14.352 +08:00 [INF] 设备 1001: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:37:14.354 +08:00 [WRN] 设备 1001 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:37:14.355 +08:00 [INF] 设备 1002: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:37:14.356 +08:00 [WRN] 设备 1002 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:37:14.357 +08:00 [INF] 设备 1003: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:37:14.358 +08:00 [WRN] 设备 1003 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:37:14.359 +08:00 [INF] 设备 1004: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:37:14.360 +08:00 [WRN] 设备 1004 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:37:14.361 +08:00 [INF] 设备 1005: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:37:14.362 +08:00 [WRN] 设备 1005 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:37:14.363 +08:00 [INF] 设备 1006: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:37:14.364 +08:00 [WRN] 设备 1006 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:37:14.364 +08:00 [INF] 设备 1007: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:37:14.366 +08:00 [WRN] 设备 1007 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:37:14.367 +08:00 [INF] 设备 1008: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:37:14.368 +08:00 [WRN] 设备 1008 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:37:14.369 +08:00 [INF] 设备 1009: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:37:14.370 +08:00 [WRN] 设备 1009 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:37:14.371 +08:00 [INF] 设备 1010: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:37:14.372 +08:00 [WRN] 设备 1010 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:37:14.373 +08:00 [INF] 设备 2001: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 11:37:14.374 +08:00 [WRN] 设备 2001 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 11:37:14.374 +08:00 [INF] === 性能报告结束 ===
2025-05-26 11:38:37.501 +08:00 [INF] 正在验证配置...
2025-05-26 11:38:37.543 +08:00 [INF] 配置验证通过
2025-05-26 11:38:37.691 +08:00 [INF] 正在启动Modbus TCP网关服务...
2025-05-26 11:38:37.693 +08:00 [INF] 创建Modbus客户端：ID=1001, IP=***************, Port=8080
2025-05-26 11:38:37.706 +08:00 [DBG] 已注册设备 1001 的线程安全客户端包装器
2025-05-26 11:38:37.706 +08:00 [INF] 已注册设备 1001 到线程安全连接池
2025-05-26 11:38:37.707 +08:00 [INF] 创建Modbus客户端：ID=1002, IP=***************, Port=8080
2025-05-26 11:38:37.708 +08:00 [DBG] 已注册设备 1002 的线程安全客户端包装器
2025-05-26 11:38:37.708 +08:00 [INF] 已注册设备 1002 到线程安全连接池
2025-05-26 11:38:37.708 +08:00 [INF] 创建Modbus客户端：ID=1003, IP=***************, Port=8080
2025-05-26 11:38:37.709 +08:00 [DBG] 已注册设备 1003 的线程安全客户端包装器
2025-05-26 11:38:37.709 +08:00 [INF] 已注册设备 1003 到线程安全连接池
2025-05-26 11:38:37.710 +08:00 [INF] 创建Modbus客户端：ID=1004, IP=***************, Port=8080
2025-05-26 11:38:37.711 +08:00 [DBG] 已注册设备 1004 的线程安全客户端包装器
2025-05-26 11:38:37.711 +08:00 [INF] 已注册设备 1004 到线程安全连接池
2025-05-26 11:38:37.711 +08:00 [INF] 创建Modbus客户端：ID=1005, IP=***************, Port=8080
2025-05-26 11:38:37.712 +08:00 [DBG] 已注册设备 1005 的线程安全客户端包装器
2025-05-26 11:38:37.712 +08:00 [INF] 已注册设备 1005 到线程安全连接池
2025-05-26 11:38:37.712 +08:00 [INF] 创建Modbus客户端：ID=1006, IP=***************, Port=8080
2025-05-26 11:38:37.713 +08:00 [DBG] 已注册设备 1006 的线程安全客户端包装器
2025-05-26 11:38:37.713 +08:00 [INF] 已注册设备 1006 到线程安全连接池
2025-05-26 11:38:37.714 +08:00 [INF] 创建Modbus客户端：ID=1007, IP=***************, Port=8080
2025-05-26 11:38:37.714 +08:00 [DBG] 已注册设备 1007 的线程安全客户端包装器
2025-05-26 11:38:37.714 +08:00 [INF] 已注册设备 1007 到线程安全连接池
2025-05-26 11:38:37.715 +08:00 [INF] 创建Modbus客户端：ID=1008, IP=***************, Port=8080
2025-05-26 11:38:37.716 +08:00 [DBG] 已注册设备 1008 的线程安全客户端包装器
2025-05-26 11:38:37.716 +08:00 [INF] 已注册设备 1008 到线程安全连接池
2025-05-26 11:38:37.716 +08:00 [INF] 创建Modbus客户端：ID=1009, IP=***************, Port=8080
2025-05-26 11:38:37.717 +08:00 [DBG] 已注册设备 1009 的线程安全客户端包装器
2025-05-26 11:38:37.717 +08:00 [INF] 已注册设备 1009 到线程安全连接池
2025-05-26 11:38:37.717 +08:00 [INF] 创建Modbus客户端：ID=1010, IP=***************, Port=8080
2025-05-26 11:38:37.718 +08:00 [DBG] 已注册设备 1010 的线程安全客户端包装器
2025-05-26 11:38:37.718 +08:00 [INF] 已注册设备 1010 到线程安全连接池
2025-05-26 11:38:37.719 +08:00 [INF] 创建Modbus客户端并连接到设备：ID=2001, PortName=COM3, BaudRate=9600
2025-05-26 11:38:37.735 +08:00 [DBG] 已注册设备 2001 的线程安全客户端包装器
2025-05-26 11:38:37.735 +08:00 [INF] 已注册设备 2001 到线程安全连接池
2025-05-26 11:38:37.740 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8080
2025-05-26 11:38:37.740 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8081
2025-05-26 11:38:37.741 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8082
2025-05-26 11:38:37.742 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8083
2025-05-26 11:38:37.742 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8084
2025-05-26 11:38:37.743 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8085
2025-05-26 11:38:37.743 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8086
2025-05-26 11:38:37.744 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8087
2025-05-26 11:38:37.744 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8088
2025-05-26 11:38:37.745 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8089
2025-05-26 11:38:37.746 +08:00 [INF] Modbus连接健康检查服务已启动
2025-05-26 11:38:37.750 +08:00 [DBG] 开始监控请求: RequestId=932b357e, DeviceId=1001, FunctionCode=0
2025-05-26 11:38:37.751 +08:00 [WRN] 设备 1001 健康检查失败，连续失败次数: 1
2025-05-26 11:38:37.753 +08:00 [DBG] 完成请求监控: RequestId=932b357e, Success=false, TotalTime=3.3185ms, ExecutionTime=3.3185ms
2025-05-26 11:38:37.754 +08:00 [WRN] 请求失败: RequestId=932b357e, DeviceId=1001, Error=Health check failed
2025-05-26 11:38:37.755 +08:00 [DBG] 开始监控请求: RequestId=32fce3df, DeviceId=1002, FunctionCode=0
2025-05-26 11:38:37.755 +08:00 [WRN] 设备 1002 健康检查失败，连续失败次数: 1
2025-05-26 11:38:37.755 +08:00 [DBG] 完成请求监控: RequestId=32fce3df, Success=false, TotalTime=0.6078ms, ExecutionTime=0.6078ms
2025-05-26 11:38:37.755 +08:00 [WRN] 请求失败: RequestId=32fce3df, DeviceId=1002, Error=Health check failed
2025-05-26 11:38:37.756 +08:00 [DBG] 开始监控请求: RequestId=3742fca4, DeviceId=1003, FunctionCode=0
2025-05-26 11:38:37.756 +08:00 [WRN] 设备 1003 健康检查失败，连续失败次数: 1
2025-05-26 11:38:37.757 +08:00 [DBG] 完成请求监控: RequestId=3742fca4, Success=false, TotalTime=1.0098ms, ExecutionTime=1.0098ms
2025-05-26 11:38:37.757 +08:00 [WRN] 请求失败: RequestId=3742fca4, DeviceId=1003, Error=Health check failed
2025-05-26 11:38:37.758 +08:00 [DBG] 开始监控请求: RequestId=f90212e9, DeviceId=1004, FunctionCode=0
2025-05-26 11:38:37.758 +08:00 [WRN] 设备 1004 健康检查失败，连续失败次数: 1
2025-05-26 11:38:37.759 +08:00 [DBG] 完成请求监控: RequestId=f90212e9, Success=false, TotalTime=0.5885ms, ExecutionTime=0.5885ms
2025-05-26 11:38:37.759 +08:00 [WRN] 请求失败: RequestId=f90212e9, DeviceId=1004, Error=Health check failed
2025-05-26 11:38:37.759 +08:00 [DBG] 开始监控请求: RequestId=fe99060f, DeviceId=1005, FunctionCode=0
2025-05-26 11:38:37.759 +08:00 [WRN] 设备 1005 健康检查失败，连续失败次数: 1
2025-05-26 11:38:37.760 +08:00 [DBG] 完成请求监控: RequestId=fe99060f, Success=false, TotalTime=0.5857ms, ExecutionTime=0.5857ms
2025-05-26 11:38:37.760 +08:00 [WRN] 请求失败: RequestId=fe99060f, DeviceId=1005, Error=Health check failed
2025-05-26 11:38:37.761 +08:00 [DBG] 开始监控请求: RequestId=ae04dead, DeviceId=1006, FunctionCode=0
2025-05-26 11:38:37.761 +08:00 [WRN] 设备 1006 健康检查失败，连续失败次数: 1
2025-05-26 11:38:37.761 +08:00 [DBG] 完成请求监控: RequestId=ae04dead, Success=false, TotalTime=0.5766ms, ExecutionTime=0.5766ms
2025-05-26 11:38:37.761 +08:00 [WRN] 请求失败: RequestId=ae04dead, DeviceId=1006, Error=Health check failed
2025-05-26 11:38:37.762 +08:00 [DBG] 开始监控请求: RequestId=553608eb, DeviceId=1007, FunctionCode=0
2025-05-26 11:38:37.762 +08:00 [WRN] 设备 1007 健康检查失败，连续失败次数: 1
2025-05-26 11:38:37.762 +08:00 [DBG] 完成请求监控: RequestId=553608eb, Success=false, TotalTime=0.5854ms, ExecutionTime=0.5854ms
2025-05-26 11:38:37.762 +08:00 [WRN] 请求失败: RequestId=553608eb, DeviceId=1007, Error=Health check failed
2025-05-26 11:38:37.763 +08:00 [DBG] 开始监控请求: RequestId=cf0086c4, DeviceId=1008, FunctionCode=0
2025-05-26 11:38:37.763 +08:00 [WRN] 设备 1008 健康检查失败，连续失败次数: 1
2025-05-26 11:38:37.764 +08:00 [DBG] 完成请求监控: RequestId=cf0086c4, Success=false, TotalTime=0.6012ms, ExecutionTime=0.6012ms
2025-05-26 11:38:37.764 +08:00 [WRN] 请求失败: RequestId=cf0086c4, DeviceId=1008, Error=Health check failed
2025-05-26 11:38:37.765 +08:00 [DBG] 开始监控请求: RequestId=d9d33acf, DeviceId=1009, FunctionCode=0
2025-05-26 11:38:37.765 +08:00 [WRN] 设备 1009 健康检查失败，连续失败次数: 1
2025-05-26 11:38:37.765 +08:00 [DBG] 完成请求监控: RequestId=d9d33acf, Success=false, TotalTime=0.5499ms, ExecutionTime=0.5499ms
2025-05-26 11:38:37.765 +08:00 [WRN] 请求失败: RequestId=d9d33acf, DeviceId=1009, Error=Health check failed
2025-05-26 11:38:37.766 +08:00 [DBG] 开始监控请求: RequestId=be1ad434, DeviceId=1010, FunctionCode=0
2025-05-26 11:38:37.766 +08:00 [WRN] 设备 1010 健康检查失败，连续失败次数: 1
2025-05-26 11:38:37.766 +08:00 [DBG] 完成请求监控: RequestId=be1ad434, Success=false, TotalTime=0.5908ms, ExecutionTime=0.5908ms
2025-05-26 11:38:37.766 +08:00 [WRN] 请求失败: RequestId=be1ad434, DeviceId=1010, Error=Health check failed
2025-05-26 11:38:37.767 +08:00 [DBG] 开始监控请求: RequestId=d7a3dfe1, DeviceId=2001, FunctionCode=0
2025-05-26 11:38:37.768 +08:00 [DBG] 开始读取保持寄存器: SlaveId=255, StartAddress=0, Quantity=1
2025-05-26 11:38:37.841 +08:00 [INF] Now listening on: http://localhost:5000
2025-05-26 11:38:37.842 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-05-26 11:38:37.842 +08:00 [INF] Hosting environment: Production
2025-05-26 11:38:37.843 +08:00 [INF] Content root path: C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway
2025-05-26 11:38:41.119 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger - null null
2025-05-26 11:38:41.147 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger - 404 0 null 29.7347ms
2025-05-26 11:38:41.151 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5000/swagger, Response status code: 404
2025-05-26 11:38:43.137 +08:00 [DBG] 设备 2001 健康检查失败: The write timed out.
2025-05-26 11:38:43.137 +08:00 [WRN] 设备 2001 健康检查失败，连续失败次数: 1
2025-05-26 11:38:43.138 +08:00 [DBG] 完成请求监控: RequestId=d7a3dfe1, Success=false, TotalTime=5370.5678ms, ExecutionTime=5370.5678ms
2025-05-26 11:38:43.138 +08:00 [WRN] 请求失败: RequestId=d7a3dfe1, DeviceId=2001, Error=Health check failed
2025-05-26 13:35:47.608 +08:00 [INF] 正在验证配置...
2025-05-26 13:35:47.640 +08:00 [INF] 配置验证通过
2025-05-26 13:35:47.699 +08:00 [INF] 正在启动Modbus TCP网关服务...
2025-05-26 13:35:47.702 +08:00 [INF] 创建Modbus客户端：ID=1001, IP=***************, Port=8080
2025-05-26 13:35:47.707 +08:00 [DBG] 已注册设备 1001 的线程安全客户端包装器
2025-05-26 13:35:47.707 +08:00 [INF] 已注册设备 1001 到线程安全连接池
2025-05-26 13:35:47.708 +08:00 [INF] 创建Modbus客户端：ID=1002, IP=***************, Port=8080
2025-05-26 13:35:47.709 +08:00 [DBG] 已注册设备 1002 的线程安全客户端包装器
2025-05-26 13:35:47.709 +08:00 [INF] 已注册设备 1002 到线程安全连接池
2025-05-26 13:35:47.709 +08:00 [INF] 创建Modbus客户端：ID=1003, IP=***************, Port=8080
2025-05-26 13:35:47.710 +08:00 [DBG] 已注册设备 1003 的线程安全客户端包装器
2025-05-26 13:35:47.710 +08:00 [INF] 已注册设备 1003 到线程安全连接池
2025-05-26 13:35:47.710 +08:00 [INF] 创建Modbus客户端：ID=1004, IP=***************, Port=8080
2025-05-26 13:35:47.711 +08:00 [DBG] 已注册设备 1004 的线程安全客户端包装器
2025-05-26 13:35:47.711 +08:00 [INF] 已注册设备 1004 到线程安全连接池
2025-05-26 13:35:47.712 +08:00 [INF] 创建Modbus客户端：ID=1005, IP=***************, Port=8080
2025-05-26 13:35:47.712 +08:00 [DBG] 已注册设备 1005 的线程安全客户端包装器
2025-05-26 13:35:47.712 +08:00 [INF] 已注册设备 1005 到线程安全连接池
2025-05-26 13:35:47.713 +08:00 [INF] 创建Modbus客户端：ID=1006, IP=***************, Port=8080
2025-05-26 13:35:47.714 +08:00 [DBG] 已注册设备 1006 的线程安全客户端包装器
2025-05-26 13:35:47.714 +08:00 [INF] 已注册设备 1006 到线程安全连接池
2025-05-26 13:35:47.715 +08:00 [INF] 创建Modbus客户端：ID=1007, IP=***************, Port=8080
2025-05-26 13:35:47.715 +08:00 [DBG] 已注册设备 1007 的线程安全客户端包装器
2025-05-26 13:35:47.715 +08:00 [INF] 已注册设备 1007 到线程安全连接池
2025-05-26 13:35:47.716 +08:00 [INF] 创建Modbus客户端：ID=1008, IP=***************, Port=8080
2025-05-26 13:35:47.716 +08:00 [DBG] 已注册设备 1008 的线程安全客户端包装器
2025-05-26 13:35:47.717 +08:00 [INF] 已注册设备 1008 到线程安全连接池
2025-05-26 13:35:47.717 +08:00 [INF] 创建Modbus客户端：ID=1009, IP=***************, Port=8080
2025-05-26 13:35:47.718 +08:00 [DBG] 已注册设备 1009 的线程安全客户端包装器
2025-05-26 13:35:47.718 +08:00 [INF] 已注册设备 1009 到线程安全连接池
2025-05-26 13:35:47.718 +08:00 [INF] 创建Modbus客户端：ID=1010, IP=***************, Port=8080
2025-05-26 13:35:47.719 +08:00 [DBG] 已注册设备 1010 的线程安全客户端包装器
2025-05-26 13:35:47.719 +08:00 [INF] 已注册设备 1010 到线程安全连接池
2025-05-26 13:35:47.720 +08:00 [INF] 创建Modbus客户端并连接到设备：ID=2001, PortName=COM3, BaudRate=9600
2025-05-26 13:35:47.725 +08:00 [DBG] 已注册设备 2001 的线程安全客户端包装器
2025-05-26 13:35:47.725 +08:00 [INF] 已注册设备 2001 到线程安全连接池
2025-05-26 13:35:47.729 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8080
2025-05-26 13:35:47.730 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8081
2025-05-26 13:35:47.730 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8082
2025-05-26 13:35:47.731 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8083
2025-05-26 13:35:47.731 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8084
2025-05-26 13:35:47.732 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8085
2025-05-26 13:35:47.732 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8086
2025-05-26 13:35:47.733 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8087
2025-05-26 13:35:47.733 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8088
2025-05-26 13:35:47.734 +08:00 [INF] Modbus TCP网关已启动，监听端口: 8089
2025-05-26 13:35:47.735 +08:00 [INF] Modbus连接健康检查服务已启动
2025-05-26 13:35:47.737 +08:00 [DBG] 开始监控请求: RequestId=9b950905, DeviceId=1001, FunctionCode=0
2025-05-26 13:35:47.738 +08:00 [WRN] 设备 1001 健康检查失败，连续失败次数: 1
2025-05-26 13:35:47.739 +08:00 [DBG] 完成请求监控: RequestId=9b950905, Success=false, TotalTime=2.3401ms, ExecutionTime=2.3401ms
2025-05-26 13:35:47.740 +08:00 [WRN] 请求失败: RequestId=9b950905, DeviceId=1001, Error=Health check failed
2025-05-26 13:35:47.742 +08:00 [DBG] 开始监控请求: RequestId=a9049b53, DeviceId=1002, FunctionCode=0
2025-05-26 13:35:47.742 +08:00 [WRN] 设备 1002 健康检查失败，连续失败次数: 1
2025-05-26 13:35:47.743 +08:00 [DBG] 完成请求监控: RequestId=a9049b53, Success=false, TotalTime=1.0212ms, ExecutionTime=1.0212ms
2025-05-26 13:35:47.743 +08:00 [WRN] 请求失败: RequestId=a9049b53, DeviceId=1002, Error=Health check failed
2025-05-26 13:35:47.743 +08:00 [DBG] 开始监控请求: RequestId=0ce38333, DeviceId=1003, FunctionCode=0
2025-05-26 13:35:47.743 +08:00 [WRN] 设备 1003 健康检查失败，连续失败次数: 1
2025-05-26 13:35:47.744 +08:00 [DBG] 完成请求监控: RequestId=0ce38333, Success=false, TotalTime=0.5832ms, ExecutionTime=0.5832ms
2025-05-26 13:35:47.744 +08:00 [WRN] 请求失败: RequestId=0ce38333, DeviceId=1003, Error=Health check failed
2025-05-26 13:35:47.744 +08:00 [DBG] 开始监控请求: RequestId=c8a76920, DeviceId=1004, FunctionCode=0
2025-05-26 13:35:47.744 +08:00 [WRN] 设备 1004 健康检查失败，连续失败次数: 1
2025-05-26 13:35:47.745 +08:00 [DBG] 完成请求监控: RequestId=c8a76920, Success=false, TotalTime=0.6524ms, ExecutionTime=0.6524ms
2025-05-26 13:35:47.745 +08:00 [WRN] 请求失败: RequestId=c8a76920, DeviceId=1004, Error=Health check failed
2025-05-26 13:35:47.746 +08:00 [DBG] 开始监控请求: RequestId=ca2923cc, DeviceId=1005, FunctionCode=0
2025-05-26 13:35:47.746 +08:00 [WRN] 设备 1005 健康检查失败，连续失败次数: 1
2025-05-26 13:35:47.747 +08:00 [DBG] 完成请求监控: RequestId=ca2923cc, Success=false, TotalTime=1.5077ms, ExecutionTime=1.5077ms
2025-05-26 13:35:47.747 +08:00 [WRN] 请求失败: RequestId=ca2923cc, DeviceId=1005, Error=Health check failed
2025-05-26 13:35:47.748 +08:00 [DBG] 开始监控请求: RequestId=2e7bea2b, DeviceId=1006, FunctionCode=0
2025-05-26 13:35:47.748 +08:00 [WRN] 设备 1006 健康检查失败，连续失败次数: 1
2025-05-26 13:35:47.749 +08:00 [DBG] 完成请求监控: RequestId=2e7bea2b, Success=false, TotalTime=0.6049ms, ExecutionTime=0.6049ms
2025-05-26 13:35:47.749 +08:00 [WRN] 请求失败: RequestId=2e7bea2b, DeviceId=1006, Error=Health check failed
2025-05-26 13:35:47.749 +08:00 [DBG] 开始监控请求: RequestId=dcd4bd5c, DeviceId=1007, FunctionCode=0
2025-05-26 13:35:47.749 +08:00 [WRN] 设备 1007 健康检查失败，连续失败次数: 1
2025-05-26 13:35:47.750 +08:00 [DBG] 完成请求监控: RequestId=dcd4bd5c, Success=false, TotalTime=0.6751ms, ExecutionTime=0.6751ms
2025-05-26 13:35:47.750 +08:00 [WRN] 请求失败: RequestId=dcd4bd5c, DeviceId=1007, Error=Health check failed
2025-05-26 13:35:47.751 +08:00 [DBG] 开始监控请求: RequestId=a8b66d90, DeviceId=1008, FunctionCode=0
2025-05-26 13:35:47.751 +08:00 [WRN] 设备 1008 健康检查失败，连续失败次数: 1
2025-05-26 13:35:47.751 +08:00 [DBG] 完成请求监控: RequestId=a8b66d90, Success=false, TotalTime=0.5895ms, ExecutionTime=0.5895ms
2025-05-26 13:35:47.751 +08:00 [WRN] 请求失败: RequestId=a8b66d90, DeviceId=1008, Error=Health check failed
2025-05-26 13:35:47.752 +08:00 [DBG] 开始监控请求: RequestId=00d1f4ce, DeviceId=1009, FunctionCode=0
2025-05-26 13:35:47.752 +08:00 [WRN] 设备 1009 健康检查失败，连续失败次数: 1
2025-05-26 13:35:47.753 +08:00 [DBG] 完成请求监控: RequestId=00d1f4ce, Success=false, TotalTime=0.6057ms, ExecutionTime=0.6057ms
2025-05-26 13:35:47.753 +08:00 [WRN] 请求失败: RequestId=00d1f4ce, DeviceId=1009, Error=Health check failed
2025-05-26 13:35:47.753 +08:00 [DBG] 开始监控请求: RequestId=d93b180e, DeviceId=1010, FunctionCode=0
2025-05-26 13:35:47.753 +08:00 [WRN] 设备 1010 健康检查失败，连续失败次数: 1
2025-05-26 13:35:47.754 +08:00 [DBG] 完成请求监控: RequestId=d93b180e, Success=false, TotalTime=1.1952ms, ExecutionTime=1.1952ms
2025-05-26 13:35:47.754 +08:00 [WRN] 请求失败: RequestId=d93b180e, DeviceId=1010, Error=Health check failed
2025-05-26 13:35:47.755 +08:00 [DBG] 开始监控请求: RequestId=24ef004a, DeviceId=2001, FunctionCode=0
2025-05-26 13:35:47.756 +08:00 [DBG] 开始读取保持寄存器: SlaveId=255, StartAddress=0, Quantity=1
2025-05-26 13:35:47.791 +08:00 [INF] Now listening on: http://localhost:5000
2025-05-26 13:35:47.792 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-05-26 13:35:47.792 +08:00 [INF] Hosting environment: Production
2025-05-26 13:35:47.793 +08:00 [INF] Content root path: C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway
2025-05-26 13:35:52.980 +08:00 [DBG] 设备 2001 健康检查失败: The write timed out.
2025-05-26 13:35:52.980 +08:00 [WRN] 设备 2001 健康检查失败，连续失败次数: 1
2025-05-26 13:35:52.981 +08:00 [DBG] 完成请求监控: RequestId=24ef004a, Success=false, TotalTime=5225.8831ms, ExecutionTime=5225.8831ms
2025-05-26 13:35:52.981 +08:00 [WRN] 请求失败: RequestId=24ef004a, DeviceId=2001, Error=Health check failed
2025-05-26 13:36:09.208 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/ - null null
2025-05-26 13:36:09.225 +08:00 [INF] Executing endpoint 'HTTP: GET /'
2025-05-26 13:36:09.227 +08:00 [INF] Executed endpoint 'HTTP: GET /'
2025-05-26 13:36:09.229 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/ - 200 null text/plain; charset=utf-8 21.5589ms
2025-05-26 13:36:17.705 +08:00 [INF] === Modbus性能报告 ===
2025-05-26 13:36:17.708 +08:00 [INF] 设备 1001: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:36:17.709 +08:00 [WRN] 设备 1001 最近错误: Health check failed
2025-05-26 13:36:17.710 +08:00 [INF] 设备 1002: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:36:17.712 +08:00 [WRN] 设备 1002 最近错误: Health check failed
2025-05-26 13:36:17.713 +08:00 [INF] 设备 1003: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:36:17.714 +08:00 [WRN] 设备 1003 最近错误: Health check failed
2025-05-26 13:36:17.715 +08:00 [INF] 设备 1004: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:36:17.716 +08:00 [WRN] 设备 1004 最近错误: Health check failed
2025-05-26 13:36:17.717 +08:00 [INF] 设备 1005: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:36:17.718 +08:00 [WRN] 设备 1005 最近错误: Health check failed
2025-05-26 13:36:17.719 +08:00 [INF] 设备 1006: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:36:17.720 +08:00 [WRN] 设备 1006 最近错误: Health check failed
2025-05-26 13:36:17.721 +08:00 [INF] 设备 1007: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:36:17.722 +08:00 [WRN] 设备 1007 最近错误: Health check failed
2025-05-26 13:36:17.723 +08:00 [INF] 设备 1008: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:36:17.724 +08:00 [WRN] 设备 1008 最近错误: Health check failed
2025-05-26 13:36:17.725 +08:00 [INF] 设备 1009: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:36:17.727 +08:00 [WRN] 设备 1009 最近错误: Health check failed
2025-05-26 13:36:17.728 +08:00 [INF] 设备 1010: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:36:17.729 +08:00 [WRN] 设备 1010 最近错误: Health check failed
2025-05-26 13:36:17.730 +08:00 [INF] 设备 2001: 总请求=1, 成功=0, 失败=1, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:36:17.731 +08:00 [WRN] 设备 2001 最近错误: Health check failed
2025-05-26 13:36:17.732 +08:00 [INF] === 性能报告结束 ===
2025-05-26 13:36:20.556 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/monitoring/version - null null
2025-05-26 13:36:20.559 +08:00 [INF] Executing endpoint 'Gsdt.ModbusGateway.Controllers.MonitoringController.GetVersion (Gsdt.ModbusGateway)'
2025-05-26 13:36:20.569 +08:00 [INF] Route matched with {action = "GetVersion", controller = "Monitoring"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[Gsdt.ModbusGateway.Controllers.VersionInfo] GetVersion() on controller Gsdt.ModbusGateway.Controllers.MonitoringController (Gsdt.ModbusGateway).
2025-05-26 13:36:20.576 +08:00 [INF] Executing OkObjectResult, writing value of type 'Gsdt.ModbusGateway.Controllers.VersionInfo'.
2025-05-26 13:36:20.609 +08:00 [INF] Executed action Gsdt.ModbusGateway.Controllers.MonitoringController.GetVersion (Gsdt.ModbusGateway) in 35.6439ms
2025-05-26 13:36:20.610 +08:00 [INF] Executed endpoint 'Gsdt.ModbusGateway.Controllers.MonitoringController.GetVersion (Gsdt.ModbusGateway)'
2025-05-26 13:36:20.611 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/monitoring/version - 200 null application/json; charset=utf-8 54.8984ms
2025-05-26 13:36:22.988 +08:00 [DBG] 开始监控请求: RequestId=649f04f4, DeviceId=1001, FunctionCode=0
2025-05-26 13:36:22.988 +08:00 [WRN] 设备 1001 健康检查失败，连续失败次数: 2
2025-05-26 13:36:22.988 +08:00 [DBG] 完成请求监控: RequestId=649f04f4, Success=false, TotalTime=0.885ms, ExecutionTime=0.885ms
2025-05-26 13:36:22.988 +08:00 [WRN] 请求失败: RequestId=649f04f4, DeviceId=1001, Error=Health check failed
2025-05-26 13:36:22.989 +08:00 [DBG] 开始监控请求: RequestId=5f1f06c5, DeviceId=1002, FunctionCode=0
2025-05-26 13:36:22.989 +08:00 [WRN] 设备 1002 健康检查失败，连续失败次数: 2
2025-05-26 13:36:22.990 +08:00 [DBG] 完成请求监控: RequestId=5f1f06c5, Success=false, TotalTime=0.613ms, ExecutionTime=0.613ms
2025-05-26 13:36:22.990 +08:00 [WRN] 请求失败: RequestId=5f1f06c5, DeviceId=1002, Error=Health check failed
2025-05-26 13:36:22.990 +08:00 [DBG] 开始监控请求: RequestId=6ed675f2, DeviceId=1003, FunctionCode=0
2025-05-26 13:36:22.990 +08:00 [WRN] 设备 1003 健康检查失败，连续失败次数: 2
2025-05-26 13:36:22.991 +08:00 [DBG] 完成请求监控: RequestId=6ed675f2, Success=false, TotalTime=0.5554ms, ExecutionTime=0.5554ms
2025-05-26 13:36:22.991 +08:00 [WRN] 请求失败: RequestId=6ed675f2, DeviceId=1003, Error=Health check failed
2025-05-26 13:36:22.991 +08:00 [DBG] 开始监控请求: RequestId=0da0ccf5, DeviceId=1004, FunctionCode=0
2025-05-26 13:36:22.991 +08:00 [WRN] 设备 1004 健康检查失败，连续失败次数: 2
2025-05-26 13:36:22.992 +08:00 [DBG] 完成请求监控: RequestId=0da0ccf5, Success=false, TotalTime=0.5379ms, ExecutionTime=0.5379ms
2025-05-26 13:36:22.992 +08:00 [WRN] 请求失败: RequestId=0da0ccf5, DeviceId=1004, Error=Health check failed
2025-05-26 13:36:22.993 +08:00 [DBG] 开始监控请求: RequestId=5880e377, DeviceId=1005, FunctionCode=0
2025-05-26 13:36:22.993 +08:00 [WRN] 设备 1005 健康检查失败，连续失败次数: 2
2025-05-26 13:36:22.994 +08:00 [DBG] 完成请求监控: RequestId=5880e377, Success=false, TotalTime=1.2817ms, ExecutionTime=1.2817ms
2025-05-26 13:36:22.994 +08:00 [WRN] 请求失败: RequestId=5880e377, DeviceId=1005, Error=Health check failed
2025-05-26 13:36:22.995 +08:00 [DBG] 开始监控请求: RequestId=bd7b1610, DeviceId=1006, FunctionCode=0
2025-05-26 13:36:22.995 +08:00 [WRN] 设备 1006 健康检查失败，连续失败次数: 2
2025-05-26 13:36:22.996 +08:00 [DBG] 完成请求监控: RequestId=bd7b1610, Success=false, TotalTime=0.6436ms, ExecutionTime=0.6436ms
2025-05-26 13:36:22.996 +08:00 [WRN] 请求失败: RequestId=bd7b1610, DeviceId=1006, Error=Health check failed
2025-05-26 13:36:22.996 +08:00 [DBG] 开始监控请求: RequestId=fb56fc37, DeviceId=1007, FunctionCode=0
2025-05-26 13:36:22.996 +08:00 [WRN] 设备 1007 健康检查失败，连续失败次数: 2
2025-05-26 13:36:22.997 +08:00 [DBG] 完成请求监控: RequestId=fb56fc37, Success=false, TotalTime=0.622ms, ExecutionTime=0.622ms
2025-05-26 13:36:22.997 +08:00 [WRN] 请求失败: RequestId=fb56fc37, DeviceId=1007, Error=Health check failed
2025-05-26 13:36:22.998 +08:00 [DBG] 开始监控请求: RequestId=10d98613, DeviceId=1008, FunctionCode=0
2025-05-26 13:36:22.998 +08:00 [WRN] 设备 1008 健康检查失败，连续失败次数: 2
2025-05-26 13:36:22.998 +08:00 [DBG] 完成请求监控: RequestId=10d98613, Success=false, TotalTime=0.5221ms, ExecutionTime=0.5221ms
2025-05-26 13:36:22.998 +08:00 [WRN] 请求失败: RequestId=10d98613, DeviceId=1008, Error=Health check failed
2025-05-26 13:36:22.999 +08:00 [DBG] 开始监控请求: RequestId=faaf6e92, DeviceId=1009, FunctionCode=0
2025-05-26 13:36:22.999 +08:00 [WRN] 设备 1009 健康检查失败，连续失败次数: 2
2025-05-26 13:36:22.999 +08:00 [DBG] 完成请求监控: RequestId=faaf6e92, Success=false, TotalTime=0.6477ms, ExecutionTime=0.6477ms
2025-05-26 13:36:22.999 +08:00 [WRN] 请求失败: RequestId=faaf6e92, DeviceId=1009, Error=Health check failed
2025-05-26 13:36:23.000 +08:00 [DBG] 开始监控请求: RequestId=eb52829b, DeviceId=1010, FunctionCode=0
2025-05-26 13:36:23.000 +08:00 [WRN] 设备 1010 健康检查失败，连续失败次数: 2
2025-05-26 13:36:23.001 +08:00 [DBG] 完成请求监控: RequestId=eb52829b, Success=false, TotalTime=0.5835ms, ExecutionTime=0.5835ms
2025-05-26 13:36:23.001 +08:00 [WRN] 请求失败: RequestId=eb52829b, DeviceId=1010, Error=Health check failed
2025-05-26 13:36:23.001 +08:00 [DBG] 开始监控请求: RequestId=625cd074, DeviceId=2001, FunctionCode=0
2025-05-26 13:36:23.001 +08:00 [DBG] 开始读取保持寄存器: SlaveId=255, StartAddress=0, Quantity=1
2025-05-26 13:36:28.210 +08:00 [DBG] 设备 2001 健康检查失败: The write timed out.
2025-05-26 13:36:28.210 +08:00 [WRN] 设备 2001 健康检查失败，连续失败次数: 2
2025-05-26 13:36:28.212 +08:00 [DBG] 完成请求监控: RequestId=625cd074, Success=false, TotalTime=5210.5866ms, ExecutionTime=5210.5866ms
2025-05-26 13:36:28.212 +08:00 [WRN] 请求失败: RequestId=625cd074, DeviceId=2001, Error=Health check failed
2025-05-26 13:36:34.249 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/api/monitoring/overview - null null
2025-05-26 13:36:34.252 +08:00 [INF] Executing endpoint 'Gsdt.ModbusGateway.Controllers.MonitoringController.GetSystemOverview (Gsdt.ModbusGateway)'
2025-05-26 13:36:34.253 +08:00 [INF] Route matched with {action = "GetSystemOverview", controller = "Monitoring"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[Gsdt.ModbusGateway.Controllers.SystemOverview] GetSystemOverview() on controller Gsdt.ModbusGateway.Controllers.MonitoringController (Gsdt.ModbusGateway).
2025-05-26 13:36:34.257 +08:00 [INF] Executing OkObjectResult, writing value of type 'Gsdt.ModbusGateway.Controllers.SystemOverview'.
2025-05-26 13:36:34.266 +08:00 [INF] Executed action Gsdt.ModbusGateway.Controllers.MonitoringController.GetSystemOverview (Gsdt.ModbusGateway) in 11.5555ms
2025-05-26 13:36:34.267 +08:00 [INF] Executed endpoint 'Gsdt.ModbusGateway.Controllers.MonitoringController.GetSystemOverview (Gsdt.ModbusGateway)'
2025-05-26 13:36:34.267 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/api/monitoring/overview - 200 null application/json; charset=utf-8 18.2275ms
2025-05-26 13:36:45.315 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger - null null
2025-05-26 13:36:45.319 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger - 301 0 null 3.9345ms
2025-05-26 13:36:45.321 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index.html - null null
2025-05-26 13:36:45.350 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index.html - 200 null text/html;charset=utf-8 28.9994ms
2025-05-26 13:36:47.695 +08:00 [INF] === Modbus性能报告 ===
2025-05-26 13:36:47.696 +08:00 [INF] 设备 1001: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:36:47.697 +08:00 [WRN] 设备 1001 最近错误: Health check failed; Health check failed
2025-05-26 13:36:47.698 +08:00 [INF] 设备 1002: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:36:47.699 +08:00 [WRN] 设备 1002 最近错误: Health check failed; Health check failed
2025-05-26 13:36:47.700 +08:00 [INF] 设备 1003: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:36:47.701 +08:00 [WRN] 设备 1003 最近错误: Health check failed; Health check failed
2025-05-26 13:36:47.701 +08:00 [INF] 设备 1004: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:36:47.703 +08:00 [WRN] 设备 1004 最近错误: Health check failed; Health check failed
2025-05-26 13:36:47.703 +08:00 [INF] 设备 1005: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:36:47.704 +08:00 [WRN] 设备 1005 最近错误: Health check failed; Health check failed
2025-05-26 13:36:47.705 +08:00 [INF] 设备 1006: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:36:47.706 +08:00 [WRN] 设备 1006 最近错误: Health check failed; Health check failed
2025-05-26 13:36:47.707 +08:00 [INF] 设备 1007: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:36:47.708 +08:00 [WRN] 设备 1007 最近错误: Health check failed; Health check failed
2025-05-26 13:36:47.710 +08:00 [INF] 设备 1008: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:36:47.711 +08:00 [WRN] 设备 1008 最近错误: Health check failed; Health check failed
2025-05-26 13:36:47.712 +08:00 [INF] 设备 1009: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:36:47.714 +08:00 [WRN] 设备 1009 最近错误: Health check failed; Health check failed
2025-05-26 13:36:47.715 +08:00 [INF] 设备 1010: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:36:47.716 +08:00 [WRN] 设备 1010 最近错误: Health check failed; Health check failed
2025-05-26 13:36:47.717 +08:00 [INF] 设备 2001: 总请求=2, 成功=0, 失败=2, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:36:47.718 +08:00 [WRN] 设备 2001 最近错误: Health check failed; Health check failed
2025-05-26 13:36:47.718 +08:00 [INF] === 性能报告结束 ===
2025-05-26 13:36:58.195 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger - null null
2025-05-26 13:36:58.197 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger - 301 0 null 1.8636ms
2025-05-26 13:36:58.199 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index.html - null null
2025-05-26 13:36:58.201 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index.html - 200 null text/html;charset=utf-8 1.5757ms
2025-05-26 13:36:58.220 +08:00 [DBG] 开始监控请求: RequestId=c26def22, DeviceId=1001, FunctionCode=0
2025-05-26 13:36:58.220 +08:00 [WRN] 设备 1001 健康检查失败，连续失败次数: 3
2025-05-26 13:36:58.221 +08:00 [DBG] 完成请求监控: RequestId=c26def22, Success=false, TotalTime=1.461ms, ExecutionTime=1.461ms
2025-05-26 13:36:58.221 +08:00 [WRN] 请求失败: RequestId=c26def22, DeviceId=1001, Error=Health check failed
2025-05-26 13:36:58.223 +08:00 [INF] 尝试重连设备 1001
2025-05-26 13:36:58.224 +08:00 [DBG] 开始监控请求: RequestId=69377f1f, DeviceId=1002, FunctionCode=0
2025-05-26 13:36:58.224 +08:00 [WRN] 设备 1002 健康检查失败，连续失败次数: 3
2025-05-26 13:36:58.225 +08:00 [DBG] 完成请求监控: RequestId=69377f1f, Success=false, TotalTime=1.1452ms, ExecutionTime=1.1452ms
2025-05-26 13:36:58.225 +08:00 [WRN] 请求失败: RequestId=69377f1f, DeviceId=1002, Error=Health check failed
2025-05-26 13:36:58.227 +08:00 [INF] 尝试重连设备 1002
2025-05-26 13:36:58.227 +08:00 [DBG] 开始监控请求: RequestId=9716bf7c, DeviceId=1003, FunctionCode=0
2025-05-26 13:36:58.227 +08:00 [WRN] 设备 1003 健康检查失败，连续失败次数: 3
2025-05-26 13:36:58.228 +08:00 [DBG] 完成请求监控: RequestId=9716bf7c, Success=false, TotalTime=0.77ms, ExecutionTime=0.77ms
2025-05-26 13:36:58.228 +08:00 [WRN] 请求失败: RequestId=9716bf7c, DeviceId=1003, Error=Health check failed
2025-05-26 13:36:58.229 +08:00 [INF] 尝试重连设备 1003
2025-05-26 13:36:58.230 +08:00 [DBG] 开始监控请求: RequestId=cda0415b, DeviceId=1004, FunctionCode=0
2025-05-26 13:36:58.230 +08:00 [WRN] 设备 1004 健康检查失败，连续失败次数: 3
2025-05-26 13:36:58.230 +08:00 [DBG] 完成请求监控: RequestId=cda0415b, Success=false, TotalTime=0.695ms, ExecutionTime=0.695ms
2025-05-26 13:36:58.230 +08:00 [WRN] 请求失败: RequestId=cda0415b, DeviceId=1004, Error=Health check failed
2025-05-26 13:36:58.231 +08:00 [INF] 尝试重连设备 1004
2025-05-26 13:36:58.232 +08:00 [DBG] 开始监控请求: RequestId=6943a43c, DeviceId=1005, FunctionCode=0
2025-05-26 13:36:58.232 +08:00 [WRN] 设备 1005 健康检查失败，连续失败次数: 3
2025-05-26 13:36:58.232 +08:00 [DBG] 完成请求监控: RequestId=6943a43c, Success=false, TotalTime=0.5897ms, ExecutionTime=0.5897ms
2025-05-26 13:36:58.232 +08:00 [WRN] 请求失败: RequestId=6943a43c, DeviceId=1005, Error=Health check failed
2025-05-26 13:36:58.233 +08:00 [INF] 尝试重连设备 1005
2025-05-26 13:36:58.234 +08:00 [DBG] 开始监控请求: RequestId=9694898f, DeviceId=1006, FunctionCode=0
2025-05-26 13:36:58.234 +08:00 [WRN] 设备 1006 健康检查失败，连续失败次数: 3
2025-05-26 13:36:58.235 +08:00 [DBG] 完成请求监控: RequestId=9694898f, Success=false, TotalTime=1.5109ms, ExecutionTime=1.5109ms
2025-05-26 13:36:58.235 +08:00 [WRN] 请求失败: RequestId=9694898f, DeviceId=1006, Error=Health check failed
2025-05-26 13:36:58.237 +08:00 [INF] 尝试重连设备 1006
2025-05-26 13:36:58.237 +08:00 [DBG] 开始监控请求: RequestId=de53ef22, DeviceId=1007, FunctionCode=0
2025-05-26 13:36:58.237 +08:00 [WRN] 设备 1007 健康检查失败，连续失败次数: 3
2025-05-26 13:36:58.238 +08:00 [DBG] 完成请求监控: RequestId=de53ef22, Success=false, TotalTime=0.8721ms, ExecutionTime=0.8721ms
2025-05-26 13:36:58.238 +08:00 [WRN] 请求失败: RequestId=de53ef22, DeviceId=1007, Error=Health check failed
2025-05-26 13:36:58.239 +08:00 [INF] 尝试重连设备 1007
2025-05-26 13:36:58.240 +08:00 [DBG] 开始监控请求: RequestId=2204de7b, DeviceId=1008, FunctionCode=0
2025-05-26 13:36:58.240 +08:00 [WRN] 设备 1008 健康检查失败，连续失败次数: 3
2025-05-26 13:36:58.240 +08:00 [DBG] 完成请求监控: RequestId=2204de7b, Success=false, TotalTime=0.5416ms, ExecutionTime=0.5416ms
2025-05-26 13:36:58.240 +08:00 [WRN] 请求失败: RequestId=2204de7b, DeviceId=1008, Error=Health check failed
2025-05-26 13:36:58.241 +08:00 [INF] 尝试重连设备 1008
2025-05-26 13:36:58.241 +08:00 [DBG] 开始监控请求: RequestId=5e39d2b0, DeviceId=1009, FunctionCode=0
2025-05-26 13:36:58.241 +08:00 [WRN] 设备 1009 健康检查失败，连续失败次数: 3
2025-05-26 13:36:58.242 +08:00 [DBG] 完成请求监控: RequestId=5e39d2b0, Success=false, TotalTime=0.7903ms, ExecutionTime=0.7903ms
2025-05-26 13:36:58.242 +08:00 [WRN] 请求失败: RequestId=5e39d2b0, DeviceId=1009, Error=Health check failed
2025-05-26 13:36:58.243 +08:00 [INF] 尝试重连设备 1009
2025-05-26 13:36:58.243 +08:00 [DBG] 开始监控请求: RequestId=0a971e3b, DeviceId=1010, FunctionCode=0
2025-05-26 13:36:58.243 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/swagger-ui.css - null null
2025-05-26 13:36:58.243 +08:00 [WRN] 设备 1010 健康检查失败，连续失败次数: 3
2025-05-26 13:36:58.245 +08:00 [DBG] 完成请求监控: RequestId=0a971e3b, Success=false, TotalTime=1.9771ms, ExecutionTime=1.9771ms
2025-05-26 13:36:58.245 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index.css - null null
2025-05-26 13:36:58.245 +08:00 [WRN] 请求失败: RequestId=0a971e3b, DeviceId=1010, Error=Health check failed
2025-05-26 13:36:58.247 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/swagger-ui-bundle.js - null null
2025-05-26 13:36:58.247 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/swagger-ui-standalone-preset.js - null null
2025-05-26 13:36:58.248 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/index.js - null null
2025-05-26 13:36:58.250 +08:00 [INF] Sending file. Request path: '/index.css'. Physical path: 'N/A'
2025-05-26 13:36:58.251 +08:00 [INF] 尝试重连设备 1010
2025-05-26 13:36:58.259 +08:00 [DBG] 开始监控请求: RequestId=2c2039e4, DeviceId=2001, FunctionCode=0
2025-05-26 13:36:58.259 +08:00 [DBG] 开始读取保持寄存器: SlaveId=255, StartAddress=0, Quantity=1
2025-05-26 13:36:58.255 +08:00 [INF] Sending file. Request path: '/swagger-ui.css'. Physical path: 'N/A'
2025-05-26 13:36:58.256 +08:00 [INF] Sending file. Request path: '/swagger-ui-standalone-preset.js'. Physical path: 'N/A'
2025-05-26 13:36:58.258 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index.js - 200 null application/javascript;charset=utf-8 9.2312ms
2025-05-26 13:36:58.258 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/index.css - 200 202 text/css 12.7472ms
2025-05-26 13:36:58.263 +08:00 [INF] Sending file. Request path: '/swagger-ui-bundle.js'. Physical path: 'N/A'
2025-05-26 13:36:58.264 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/swagger-ui.css - 200 152034 text/css 20.9019ms
2025-05-26 13:36:58.264 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/swagger-ui-standalone-preset.js - 200 230293 text/javascript 17.2637ms
2025-05-26 13:36:58.271 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/swagger-ui-bundle.js - 200 1452753 text/javascript 24.0327ms
2025-05-26 13:36:58.786 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/v1/swagger.json - null null
2025-05-26 13:36:58.802 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/swagger/favicon-32x32.png - null null
2025-05-26 13:36:58.803 +08:00 [INF] Sending file. Request path: '/favicon-32x32.png'. Physical path: 'N/A'
2025-05-26 13:36:58.804 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/favicon-32x32.png - 200 628 image/png 2.5068ms
2025-05-26 13:36:58.922 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 136.4994ms
2025-05-26 13:37:03.467 +08:00 [DBG] 设备 2001 健康检查失败: The write timed out.
2025-05-26 13:37:03.467 +08:00 [WRN] 设备 2001 健康检查失败，连续失败次数: 3
2025-05-26 13:37:03.469 +08:00 [DBG] 完成请求监控: RequestId=2c2039e4, Success=false, TotalTime=5209.4319ms, ExecutionTime=5209.4319ms
2025-05-26 13:37:03.469 +08:00 [WRN] 请求失败: RequestId=2c2039e4, DeviceId=2001, Error=Health check failed
2025-05-26 13:37:03.470 +08:00 [INF] 尝试重连设备 2001
2025-05-26 13:37:04.478 +08:00 [INF] 成功重连RTU设备 2001 (COM3)
2025-05-26 13:37:17.706 +08:00 [INF] === Modbus性能报告 ===
2025-05-26 13:37:17.708 +08:00 [INF] 设备 1001: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:37:17.711 +08:00 [WRN] 设备 1001 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:37:17.712 +08:00 [INF] 设备 1002: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:37:17.714 +08:00 [WRN] 设备 1002 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:37:17.714 +08:00 [INF] 设备 1003: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:37:17.716 +08:00 [WRN] 设备 1003 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:37:17.716 +08:00 [INF] 设备 1004: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:37:17.717 +08:00 [WRN] 设备 1004 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:37:17.718 +08:00 [INF] 设备 1005: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:37:17.719 +08:00 [WRN] 设备 1005 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:37:17.719 +08:00 [INF] 设备 1006: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:37:17.721 +08:00 [WRN] 设备 1006 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:37:17.722 +08:00 [INF] 设备 1007: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:37:17.723 +08:00 [WRN] 设备 1007 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:37:17.724 +08:00 [INF] 设备 1008: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:37:17.725 +08:00 [WRN] 设备 1008 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:37:17.725 +08:00 [INF] 设备 1009: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:37:17.726 +08:00 [WRN] 设备 1009 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:37:17.727 +08:00 [INF] 设备 1010: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:37:17.728 +08:00 [WRN] 设备 1010 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:37:17.728 +08:00 [INF] 设备 2001: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:37:17.730 +08:00 [WRN] 设备 2001 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:37:17.730 +08:00 [INF] === 性能报告结束 ===
2025-05-26 13:37:20.275 +08:00 [ERR] 重连设备 1001 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 13:37:20.284 +08:00 [ERR] 重连设备 1006 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 13:37:20.284 +08:00 [ERR] 重连设备 1002 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 13:37:20.284 +08:00 [ERR] 重连设备 1003 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 13:37:20.284 +08:00 [ERR] 重连设备 1007 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 13:37:20.284 +08:00 [ERR] 重连设备 1004 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 13:37:20.284 +08:00 [ERR] 重连设备 1005 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 13:37:20.284 +08:00 [ERR] 重连设备 1008 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 13:37:20.300 +08:00 [ERR] 重连设备 1009 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 13:37:20.315 +08:00 [ERR] 重连设备 1010 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 13:37:47.707 +08:00 [INF] === Modbus性能报告 ===
2025-05-26 13:37:47.707 +08:00 [INF] 设备 1001: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:37:47.708 +08:00 [WRN] 设备 1001 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:37:47.709 +08:00 [INF] 设备 1002: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:37:47.710 +08:00 [WRN] 设备 1002 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:37:47.711 +08:00 [INF] 设备 1003: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:37:47.712 +08:00 [WRN] 设备 1003 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:37:47.712 +08:00 [INF] 设备 1004: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:37:47.713 +08:00 [WRN] 设备 1004 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:37:47.714 +08:00 [INF] 设备 1005: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:37:47.715 +08:00 [WRN] 设备 1005 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:37:47.716 +08:00 [INF] 设备 1006: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:37:47.717 +08:00 [WRN] 设备 1006 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:37:47.717 +08:00 [INF] 设备 1007: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:37:47.719 +08:00 [WRN] 设备 1007 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:37:47.719 +08:00 [INF] 设备 1008: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:37:47.721 +08:00 [WRN] 设备 1008 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:37:47.721 +08:00 [INF] 设备 1009: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:37:47.723 +08:00 [WRN] 设备 1009 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:37:47.723 +08:00 [INF] 设备 1010: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:37:47.724 +08:00 [WRN] 设备 1010 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:37:47.725 +08:00 [INF] 设备 2001: 总请求=3, 成功=0, 失败=3, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:37:47.726 +08:00 [WRN] 设备 2001 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:37:47.726 +08:00 [INF] === 性能报告结束 ===
2025-05-26 13:37:50.326 +08:00 [DBG] 开始监控请求: RequestId=1073c95d, DeviceId=1001, FunctionCode=0
2025-05-26 13:37:50.326 +08:00 [WRN] 设备 1001 健康检查失败，连续失败次数: 4
2025-05-26 13:37:50.327 +08:00 [DBG] 完成请求监控: RequestId=1073c95d, Success=false, TotalTime=1.2207ms, ExecutionTime=1.2207ms
2025-05-26 13:37:50.328 +08:00 [WRN] 请求失败: RequestId=1073c95d, DeviceId=1001, Error=Health check failed
2025-05-26 13:37:50.329 +08:00 [INF] 尝试重连设备 1001
2025-05-26 13:37:50.329 +08:00 [DBG] 开始监控请求: RequestId=69c1f435, DeviceId=1002, FunctionCode=0
2025-05-26 13:37:50.329 +08:00 [WRN] 设备 1002 健康检查失败，连续失败次数: 4
2025-05-26 13:37:50.330 +08:00 [DBG] 完成请求监控: RequestId=69c1f435, Success=false, TotalTime=0.8147ms, ExecutionTime=0.8147ms
2025-05-26 13:37:50.330 +08:00 [WRN] 请求失败: RequestId=69c1f435, DeviceId=1002, Error=Health check failed
2025-05-26 13:37:50.331 +08:00 [INF] 尝试重连设备 1002
2025-05-26 13:37:50.332 +08:00 [DBG] 开始监控请求: RequestId=be69f438, DeviceId=1003, FunctionCode=0
2025-05-26 13:37:50.332 +08:00 [WRN] 设备 1003 健康检查失败，连续失败次数: 4
2025-05-26 13:37:50.333 +08:00 [DBG] 完成请求监控: RequestId=be69f438, Success=false, TotalTime=0.7103ms, ExecutionTime=0.7103ms
2025-05-26 13:37:50.333 +08:00 [WRN] 请求失败: RequestId=be69f438, DeviceId=1003, Error=Health check failed
2025-05-26 13:37:50.334 +08:00 [INF] 尝试重连设备 1003
2025-05-26 13:37:50.334 +08:00 [DBG] 开始监控请求: RequestId=e1190a7a, DeviceId=1004, FunctionCode=0
2025-05-26 13:37:50.334 +08:00 [WRN] 设备 1004 健康检查失败，连续失败次数: 4
2025-05-26 13:37:50.335 +08:00 [DBG] 完成请求监控: RequestId=e1190a7a, Success=false, TotalTime=0.6875ms, ExecutionTime=0.6875ms
2025-05-26 13:37:50.335 +08:00 [WRN] 请求失败: RequestId=e1190a7a, DeviceId=1004, Error=Health check failed
2025-05-26 13:37:50.336 +08:00 [INF] 尝试重连设备 1004
2025-05-26 13:37:50.336 +08:00 [DBG] 开始监控请求: RequestId=59d91ed2, DeviceId=1005, FunctionCode=0
2025-05-26 13:37:50.336 +08:00 [WRN] 设备 1005 健康检查失败，连续失败次数: 4
2025-05-26 13:37:50.337 +08:00 [DBG] 完成请求监控: RequestId=59d91ed2, Success=false, TotalTime=0.499ms, ExecutionTime=0.499ms
2025-05-26 13:37:50.337 +08:00 [WRN] 请求失败: RequestId=59d91ed2, DeviceId=1005, Error=Health check failed
2025-05-26 13:37:50.337 +08:00 [INF] 尝试重连设备 1005
2025-05-26 13:37:50.338 +08:00 [DBG] 开始监控请求: RequestId=11070bb3, DeviceId=1006, FunctionCode=0
2025-05-26 13:37:50.338 +08:00 [WRN] 设备 1006 健康检查失败，连续失败次数: 4
2025-05-26 13:37:50.338 +08:00 [DBG] 完成请求监控: RequestId=11070bb3, Success=false, TotalTime=0.4929ms, ExecutionTime=0.4929ms
2025-05-26 13:37:50.338 +08:00 [WRN] 请求失败: RequestId=11070bb3, DeviceId=1006, Error=Health check failed
2025-05-26 13:37:50.339 +08:00 [INF] 尝试重连设备 1006
2025-05-26 13:37:50.339 +08:00 [DBG] 开始监控请求: RequestId=52dcf139, DeviceId=1007, FunctionCode=0
2025-05-26 13:37:50.339 +08:00 [WRN] 设备 1007 健康检查失败，连续失败次数: 4
2025-05-26 13:37:50.340 +08:00 [DBG] 完成请求监控: RequestId=52dcf139, Success=false, TotalTime=0.4703ms, ExecutionTime=0.4703ms
2025-05-26 13:37:50.340 +08:00 [WRN] 请求失败: RequestId=52dcf139, DeviceId=1007, Error=Health check failed
2025-05-26 13:37:50.340 +08:00 [INF] 尝试重连设备 1007
2025-05-26 13:37:50.341 +08:00 [DBG] 开始监控请求: RequestId=ae61d20c, DeviceId=1008, FunctionCode=0
2025-05-26 13:37:50.341 +08:00 [WRN] 设备 1008 健康检查失败，连续失败次数: 4
2025-05-26 13:37:50.341 +08:00 [DBG] 完成请求监控: RequestId=ae61d20c, Success=false, TotalTime=0.5074ms, ExecutionTime=0.5074ms
2025-05-26 13:37:50.341 +08:00 [WRN] 请求失败: RequestId=ae61d20c, DeviceId=1008, Error=Health check failed
2025-05-26 13:37:50.342 +08:00 [INF] 尝试重连设备 1008
2025-05-26 13:37:50.342 +08:00 [DBG] 开始监控请求: RequestId=7d8aa556, DeviceId=1009, FunctionCode=0
2025-05-26 13:37:50.342 +08:00 [WRN] 设备 1009 健康检查失败，连续失败次数: 4
2025-05-26 13:37:50.343 +08:00 [DBG] 完成请求监控: RequestId=7d8aa556, Success=false, TotalTime=0.5199ms, ExecutionTime=0.5199ms
2025-05-26 13:37:50.343 +08:00 [WRN] 请求失败: RequestId=7d8aa556, DeviceId=1009, Error=Health check failed
2025-05-26 13:37:50.343 +08:00 [INF] 尝试重连设备 1009
2025-05-26 13:37:50.344 +08:00 [DBG] 开始监控请求: RequestId=527f584e, DeviceId=1010, FunctionCode=0
2025-05-26 13:37:50.344 +08:00 [WRN] 设备 1010 健康检查失败，连续失败次数: 4
2025-05-26 13:37:50.344 +08:00 [DBG] 完成请求监控: RequestId=527f584e, Success=false, TotalTime=0.5283ms, ExecutionTime=0.5283ms
2025-05-26 13:37:50.344 +08:00 [WRN] 请求失败: RequestId=527f584e, DeviceId=1010, Error=Health check failed
2025-05-26 13:37:50.345 +08:00 [INF] 尝试重连设备 1010
2025-05-26 13:37:50.346 +08:00 [DBG] 开始监控请求: RequestId=80ac8160, DeviceId=2001, FunctionCode=0
2025-05-26 13:37:50.346 +08:00 [DBG] 开始读取保持寄存器: SlaveId=255, StartAddress=0, Quantity=1
2025-05-26 13:37:55.555 +08:00 [DBG] 设备 2001 健康检查失败: The write timed out.
2025-05-26 13:37:55.555 +08:00 [WRN] 设备 2001 健康检查失败，连续失败次数: 4
2025-05-26 13:37:55.555 +08:00 [DBG] 完成请求监控: RequestId=80ac8160, Success=false, TotalTime=5209.6334ms, ExecutionTime=5209.6334ms
2025-05-26 13:37:55.555 +08:00 [WRN] 请求失败: RequestId=80ac8160, DeviceId=2001, Error=Health check failed
2025-05-26 13:37:55.556 +08:00 [INF] 尝试重连设备 2001
2025-05-26 13:37:56.566 +08:00 [INF] 成功重连RTU设备 2001 (COM3)
2025-05-26 13:38:12.381 +08:00 [ERR] 重连设备 1005 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 13:38:12.381 +08:00 [ERR] 重连设备 1003 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 13:38:12.381 +08:00 [ERR] 重连设备 1001 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 13:38:12.381 +08:00 [ERR] 重连设备 1006 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 13:38:12.381 +08:00 [ERR] 重连设备 1002 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 13:38:12.382 +08:00 [ERR] 重连设备 1004 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 13:38:12.397 +08:00 [ERR] 重连设备 1010 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 13:38:12.397 +08:00 [ERR] 重连设备 1008 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 13:38:12.397 +08:00 [ERR] 重连设备 1009 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 13:38:12.397 +08:00 [ERR] 重连设备 1007 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 13:38:17.693 +08:00 [INF] === Modbus性能报告 ===
2025-05-26 13:38:17.693 +08:00 [INF] 设备 1001: 总请求=4, 成功=0, 失败=4, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:38:17.695 +08:00 [WRN] 设备 1001 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:38:17.695 +08:00 [INF] 设备 1002: 总请求=4, 成功=0, 失败=4, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:38:17.696 +08:00 [WRN] 设备 1002 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:38:17.697 +08:00 [INF] 设备 1003: 总请求=4, 成功=0, 失败=4, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:38:17.698 +08:00 [WRN] 设备 1003 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:38:17.698 +08:00 [INF] 设备 1004: 总请求=4, 成功=0, 失败=4, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:38:17.700 +08:00 [WRN] 设备 1004 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:38:17.700 +08:00 [INF] 设备 1005: 总请求=4, 成功=0, 失败=4, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:38:17.701 +08:00 [WRN] 设备 1005 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:38:17.702 +08:00 [INF] 设备 1006: 总请求=4, 成功=0, 失败=4, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:38:17.703 +08:00 [WRN] 设备 1006 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:38:17.704 +08:00 [INF] 设备 1007: 总请求=4, 成功=0, 失败=4, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:38:17.705 +08:00 [WRN] 设备 1007 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:38:17.706 +08:00 [INF] 设备 1008: 总请求=4, 成功=0, 失败=4, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:38:17.707 +08:00 [WRN] 设备 1008 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:38:17.707 +08:00 [INF] 设备 1009: 总请求=4, 成功=0, 失败=4, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:38:17.709 +08:00 [WRN] 设备 1009 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:38:17.709 +08:00 [INF] 设备 1010: 总请求=4, 成功=0, 失败=4, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:38:17.710 +08:00 [WRN] 设备 1010 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:38:17.711 +08:00 [INF] 设备 2001: 总请求=4, 成功=0, 失败=4, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:38:17.712 +08:00 [WRN] 设备 2001 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:38:17.713 +08:00 [INF] === 性能报告结束 ===
2025-05-26 13:38:42.414 +08:00 [DBG] 开始监控请求: RequestId=df2d74c6, DeviceId=1001, FunctionCode=0
2025-05-26 13:38:42.414 +08:00 [WRN] 设备 1001 健康检查失败，连续失败次数: 5
2025-05-26 13:38:42.416 +08:00 [DBG] 完成请求监控: RequestId=df2d74c6, Success=false, TotalTime=2.0829ms, ExecutionTime=2.0829ms
2025-05-26 13:38:42.416 +08:00 [WRN] 请求失败: RequestId=df2d74c6, DeviceId=1001, Error=Health check failed
2025-05-26 13:38:42.418 +08:00 [INF] 尝试重连设备 1001
2025-05-26 13:38:42.419 +08:00 [DBG] 开始监控请求: RequestId=3475b0a8, DeviceId=1002, FunctionCode=0
2025-05-26 13:38:42.419 +08:00 [WRN] 设备 1002 健康检查失败，连续失败次数: 5
2025-05-26 13:38:42.420 +08:00 [DBG] 完成请求监控: RequestId=3475b0a8, Success=false, TotalTime=1.0146ms, ExecutionTime=1.0146ms
2025-05-26 13:38:42.420 +08:00 [WRN] 请求失败: RequestId=3475b0a8, DeviceId=1002, Error=Health check failed
2025-05-26 13:38:42.421 +08:00 [INF] 尝试重连设备 1002
2025-05-26 13:38:42.422 +08:00 [DBG] 开始监控请求: RequestId=8432efb1, DeviceId=1003, FunctionCode=0
2025-05-26 13:38:42.422 +08:00 [WRN] 设备 1003 健康检查失败，连续失败次数: 5
2025-05-26 13:38:42.423 +08:00 [DBG] 完成请求监控: RequestId=8432efb1, Success=false, TotalTime=0.8432ms, ExecutionTime=0.8432ms
2025-05-26 13:38:42.423 +08:00 [WRN] 请求失败: RequestId=8432efb1, DeviceId=1003, Error=Health check failed
2025-05-26 13:38:42.424 +08:00 [INF] 尝试重连设备 1003
2025-05-26 13:38:42.424 +08:00 [DBG] 开始监控请求: RequestId=9b4abcde, DeviceId=1004, FunctionCode=0
2025-05-26 13:38:42.424 +08:00 [WRN] 设备 1004 健康检查失败，连续失败次数: 5
2025-05-26 13:38:42.425 +08:00 [DBG] 完成请求监控: RequestId=9b4abcde, Success=false, TotalTime=0.6889ms, ExecutionTime=0.6889ms
2025-05-26 13:38:42.425 +08:00 [WRN] 请求失败: RequestId=9b4abcde, DeviceId=1004, Error=Health check failed
2025-05-26 13:38:42.425 +08:00 [INF] 尝试重连设备 1004
2025-05-26 13:38:42.426 +08:00 [DBG] 开始监控请求: RequestId=eb17a17e, DeviceId=1005, FunctionCode=0
2025-05-26 13:38:42.426 +08:00 [WRN] 设备 1005 健康检查失败，连续失败次数: 5
2025-05-26 13:38:42.426 +08:00 [DBG] 完成请求监控: RequestId=eb17a17e, Success=false, TotalTime=0.4618ms, ExecutionTime=0.4618ms
2025-05-26 13:38:42.426 +08:00 [WRN] 请求失败: RequestId=eb17a17e, DeviceId=1005, Error=Health check failed
2025-05-26 13:38:42.427 +08:00 [INF] 尝试重连设备 1005
2025-05-26 13:38:42.427 +08:00 [DBG] 开始监控请求: RequestId=3033f438, DeviceId=1006, FunctionCode=0
2025-05-26 13:38:42.427 +08:00 [WRN] 设备 1006 健康检查失败，连续失败次数: 5
2025-05-26 13:38:42.428 +08:00 [DBG] 完成请求监控: RequestId=3033f438, Success=false, TotalTime=0.481ms, ExecutionTime=0.481ms
2025-05-26 13:38:42.428 +08:00 [WRN] 请求失败: RequestId=3033f438, DeviceId=1006, Error=Health check failed
2025-05-26 13:38:42.428 +08:00 [INF] 尝试重连设备 1006
2025-05-26 13:38:42.429 +08:00 [DBG] 开始监控请求: RequestId=cc19c35e, DeviceId=1007, FunctionCode=0
2025-05-26 13:38:42.429 +08:00 [WRN] 设备 1007 健康检查失败，连续失败次数: 5
2025-05-26 13:38:42.429 +08:00 [DBG] 完成请求监控: RequestId=cc19c35e, Success=false, TotalTime=0.4549ms, ExecutionTime=0.4549ms
2025-05-26 13:38:42.429 +08:00 [WRN] 请求失败: RequestId=cc19c35e, DeviceId=1007, Error=Health check failed
2025-05-26 13:38:42.430 +08:00 [INF] 尝试重连设备 1007
2025-05-26 13:38:42.430 +08:00 [DBG] 开始监控请求: RequestId=683efba3, DeviceId=1008, FunctionCode=0
2025-05-26 13:38:42.430 +08:00 [WRN] 设备 1008 健康检查失败，连续失败次数: 5
2025-05-26 13:38:42.431 +08:00 [DBG] 完成请求监控: RequestId=683efba3, Success=false, TotalTime=0.7533ms, ExecutionTime=0.7533ms
2025-05-26 13:38:42.431 +08:00 [WRN] 请求失败: RequestId=683efba3, DeviceId=1008, Error=Health check failed
2025-05-26 13:38:42.432 +08:00 [INF] 尝试重连设备 1008
2025-05-26 13:38:42.432 +08:00 [DBG] 开始监控请求: RequestId=d0d7b619, DeviceId=1009, FunctionCode=0
2025-05-26 13:38:42.432 +08:00 [WRN] 设备 1009 健康检查失败，连续失败次数: 5
2025-05-26 13:38:42.433 +08:00 [DBG] 完成请求监控: RequestId=d0d7b619, Success=false, TotalTime=0.5465ms, ExecutionTime=0.5465ms
2025-05-26 13:38:42.433 +08:00 [WRN] 请求失败: RequestId=d0d7b619, DeviceId=1009, Error=Health check failed
2025-05-26 13:38:42.433 +08:00 [INF] 尝试重连设备 1009
2025-05-26 13:38:42.434 +08:00 [DBG] 开始监控请求: RequestId=b1475ba8, DeviceId=1010, FunctionCode=0
2025-05-26 13:38:42.434 +08:00 [WRN] 设备 1010 健康检查失败，连续失败次数: 5
2025-05-26 13:38:42.434 +08:00 [DBG] 完成请求监控: RequestId=b1475ba8, Success=false, TotalTime=0.4668ms, ExecutionTime=0.4668ms
2025-05-26 13:38:42.434 +08:00 [WRN] 请求失败: RequestId=b1475ba8, DeviceId=1010, Error=Health check failed
2025-05-26 13:38:42.435 +08:00 [INF] 尝试重连设备 1010
2025-05-26 13:38:42.435 +08:00 [DBG] 开始监控请求: RequestId=248af3a2, DeviceId=2001, FunctionCode=0
2025-05-26 13:38:42.435 +08:00 [DBG] 开始读取保持寄存器: SlaveId=255, StartAddress=0, Quantity=1
2025-05-26 13:38:45.178 +08:00 [INF] Application is shutting down...
2025-05-26 13:38:47.674 +08:00 [DBG] 设备 2001 健康检查失败: The write timed out.
2025-05-26 13:38:47.674 +08:00 [WRN] 设备 2001 健康检查失败，连续失败次数: 5
2025-05-26 13:38:47.674 +08:00 [DBG] 完成请求监控: RequestId=248af3a2, Success=false, TotalTime=5239.2607ms, ExecutionTime=5239.2607ms
2025-05-26 13:38:47.674 +08:00 [WRN] 请求失败: RequestId=248af3a2, DeviceId=2001, Error=Health check failed
2025-05-26 13:38:47.675 +08:00 [INF] 尝试重连设备 2001
2025-05-26 13:38:47.676 +08:00 [DBG] 断开设备 2001 连接时发生错误: A task was canceled.
2025-05-26 13:38:47.676 +08:00 [ERR] 重连设备 2001 失败
System.Threading.Tasks.TaskCanceledException: A task was canceled.
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 229
2025-05-26 13:38:47.705 +08:00 [INF] === Modbus性能报告 ===
2025-05-26 13:38:47.706 +08:00 [INF] 设备 1001: 总请求=5, 成功=0, 失败=5, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:38:47.707 +08:00 [WRN] 设备 1001 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:38:47.707 +08:00 [INF] 设备 1002: 总请求=5, 成功=0, 失败=5, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:38:47.708 +08:00 [WRN] 设备 1002 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:38:47.709 +08:00 [INF] 设备 1003: 总请求=5, 成功=0, 失败=5, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:38:47.710 +08:00 [WRN] 设备 1003 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:38:47.710 +08:00 [INF] 设备 1004: 总请求=5, 成功=0, 失败=5, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:38:47.712 +08:00 [WRN] 设备 1004 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:38:47.712 +08:00 [INF] 设备 1005: 总请求=5, 成功=0, 失败=5, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:38:47.714 +08:00 [WRN] 设备 1005 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:38:47.715 +08:00 [INF] 设备 1006: 总请求=5, 成功=0, 失败=5, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:38:47.716 +08:00 [WRN] 设备 1006 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:38:47.716 +08:00 [INF] 设备 1007: 总请求=5, 成功=0, 失败=5, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:38:47.717 +08:00 [WRN] 设备 1007 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:38:47.718 +08:00 [INF] 设备 1008: 总请求=5, 成功=0, 失败=5, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:38:47.719 +08:00 [WRN] 设备 1008 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:38:47.720 +08:00 [INF] 设备 1009: 总请求=5, 成功=0, 失败=5, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:38:47.721 +08:00 [WRN] 设备 1009 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:38:47.722 +08:00 [INF] 设备 1010: 总请求=5, 成功=0, 失败=5, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:38:47.723 +08:00 [WRN] 设备 1010 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:38:47.723 +08:00 [INF] 设备 2001: 总请求=5, 成功=0, 失败=5, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:38:47.724 +08:00 [WRN] 设备 2001 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:38:47.725 +08:00 [INF] === 性能报告结束 ===
2025-05-26 13:39:04.459 +08:00 [ERR] 重连设备 1001 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 13:39:04.459 +08:00 [ERR] 重连设备 1003 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 13:39:04.460 +08:00 [ERR] 重连设备 1002 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 13:39:04.474 +08:00 [ERR] 重连设备 1006 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 13:39:04.474 +08:00 [ERR] 重连设备 1005 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 13:39:04.474 +08:00 [ERR] 重连设备 1007 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 13:39:04.474 +08:00 [ERR] 重连设备 1004 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 13:39:04.474 +08:00 [ERR] 重连设备 1008 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 13:39:04.474 +08:00 [ERR] 重连设备 1010 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 13:39:04.474 +08:00 [ERR] 重连设备 1009 失败
System.Net.Sockets.SocketException (10060): 由于连接方在一段时间后没有正确答复或连接的主机没有反应，连接尝试失败。 [::ffff:***************]:8080
   at System.Net.Sockets.Socket.DoConnect(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Connect(EndPoint remoteEP)
   at System.Net.Sockets.TcpClient.Connect(IPEndPoint remoteEP)
   at Gsdt.ModbusGateway.Models.ModbusTcpClient.Connect(IPEndPoint endPoint) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Models\ModbusTcpClient.cs:line 32
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.<>c__DisplayClass12_3.<AttemptReconnectAsync>b__2() in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 237
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunFromThreadPoolDispatchLoop(Thread threadPoolThread, ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at Gsdt.ModbusGateway.Services.ModbusConnectionHealthService.AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken) in C:\Users\<USER>\Documents\WorkSpace\Gsdt.ModbusGateway\src\Gsdt.ModbusGateway\Services\ModbusConnectionHealthService.cs:line 236
2025-05-26 13:39:04.486 +08:00 [INF] 连接健康检查服务正在停止
2025-05-26 13:39:04.487 +08:00 [INF] 正在停止Modbus TCP网关服务...
2025-05-26 13:39:04.488 +08:00 [INF] Modbus TCP网关服务已停止
2025-05-26 13:39:04.492 +08:00 [DBG] 正在释放线程安全ModbusClient连接池资源
2025-05-26 13:39:04.492 +08:00 [DBG] 线程安全ModbusClient连接池资源已释放
2025-05-26 13:39:04.492 +08:00 [INF] 正在输出最终性能报告...
2025-05-26 13:39:04.493 +08:00 [INF] === Modbus性能报告 ===
2025-05-26 13:39:04.493 +08:00 [INF] 设备 1001: 总请求=5, 成功=0, 失败=5, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:39:04.494 +08:00 [WRN] 设备 1001 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:39:04.495 +08:00 [INF] 设备 1002: 总请求=5, 成功=0, 失败=5, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:39:04.496 +08:00 [WRN] 设备 1002 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:39:04.496 +08:00 [INF] 设备 1003: 总请求=5, 成功=0, 失败=5, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:39:04.497 +08:00 [WRN] 设备 1003 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:39:04.498 +08:00 [INF] 设备 1004: 总请求=5, 成功=0, 失败=5, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:39:04.499 +08:00 [WRN] 设备 1004 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:39:04.500 +08:00 [INF] 设备 1005: 总请求=5, 成功=0, 失败=5, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:39:04.501 +08:00 [WRN] 设备 1005 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:39:04.501 +08:00 [INF] 设备 1006: 总请求=5, 成功=0, 失败=5, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:39:04.502 +08:00 [WRN] 设备 1006 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:39:04.503 +08:00 [INF] 设备 1007: 总请求=5, 成功=0, 失败=5, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:39:04.504 +08:00 [WRN] 设备 1007 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:39:04.504 +08:00 [INF] 设备 1008: 总请求=5, 成功=0, 失败=5, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:39:04.506 +08:00 [WRN] 设备 1008 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:39:04.506 +08:00 [INF] 设备 1009: 总请求=5, 成功=0, 失败=5, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:39:04.508 +08:00 [WRN] 设备 1009 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:39:04.508 +08:00 [INF] 设备 1010: 总请求=5, 成功=0, 失败=5, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:39:04.509 +08:00 [WRN] 设备 1010 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:39:04.510 +08:00 [INF] 设备 2001: 总请求=5, 成功=0, 失败=5, 活跃请求=0, 成功率=0.0%, 平均响应时间=0.0ms, 平均排队时间=0.0ms
2025-05-26 13:39:04.511 +08:00 [WRN] 设备 2001 最近错误: Health check failed; Health check failed; Health check failed
2025-05-26 13:39:04.511 +08:00 [INF] === 性能报告结束 ===
