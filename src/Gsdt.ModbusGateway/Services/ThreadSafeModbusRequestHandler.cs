using Gsdt.ModbusGateway.Models;
using Gsdt.ModbusGateway.Services.ResponseMergers;
using Microsoft.Extensions.Logging;

namespace Gsdt.ModbusGateway.Services;

/// <summary>
/// 线程安全的Modbus请求处理器
/// 使用ThreadSafeModbusClientPool确保请求的串行处理
/// </summary>
public class ThreadSafeModbusRequestHandler : IModbusRequestHandler
{
    private readonly ILogger<ThreadSafeModbusRequestHandler> _logger;
    private readonly ThreadSafeModbusClientPool _clientPool;

    public ThreadSafeModbusRequestHandler(
        ILogger<ThreadSafeModbusRequestHandler> logger,
        ThreadSafeModbusClientPool clientPool)
    {
        _logger = logger;
        _clientPool = clientPool;
    }

    /// <summary>
    /// 转发请求到目标设备（线程安全版本）
    /// </summary>
    /// <param name="client">原始ModbusClient（用于获取设备ID）</param>
    /// <param name="request">请求数据</param>
    /// <param name="originSlaveId">原始从站ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>响应数据</returns>
    public async Task<byte[]> ForwardRequestAsync(ModbusClient client, byte[] request, byte originSlaveId, CancellationToken cancellationToken)
    {
        // 获取从站ID和功能码
        var slaveId = request[6];
        var functionCode = request[7];
        
        // 找到对应的设备ID（这里需要从client反向查找deviceId）
        var deviceId = GetDeviceIdFromClient(client);
        if (deviceId == null)
        {
            throw new InvalidOperationException("无法找到对应的设备ID");
        }

        // 获取线程安全的客户端包装器
        var clientWrapper = _clientPool.GetClientWrapper(deviceId.Value);
        if (clientWrapper == null)
        {
            throw new InvalidOperationException($"未找到设备 {deviceId} 的客户端包装器");
        }
        
        // 根据功能码处理请求
        switch (functionCode)
        {
            case 1: // 读取线圈
                return await HandleReadCoilsAsync(clientWrapper, request, originSlaveId, cancellationToken);
            case 2: // 读取离散输入
                return await HandleReadDiscreteInputsAsync(clientWrapper, request, originSlaveId, cancellationToken);
            case 3: // 读取保持寄存器
                return await HandleReadHoldingRegistersAsync(clientWrapper, request, originSlaveId, cancellationToken);
            case 4: // 读取输入寄存器
                return await HandleReadInputRegistersAsync(clientWrapper, request, originSlaveId, cancellationToken);
            case 5: // 写入单个线圈
                return await HandleWriteSingleCoilAsync(clientWrapper, request, originSlaveId, cancellationToken);
            case 6: // 写入单个寄存器
                return await HandleWriteSingleRegisterAsync(clientWrapper, request, originSlaveId, cancellationToken);
            case 15: // 写入多个线圈
                return await HandleWriteMultipleCoilsAsync(clientWrapper, request, originSlaveId, cancellationToken);
            case 16: // 写入多个寄存器
                return await HandleWriteMultipleRegistersAsync(clientWrapper, request, originSlaveId, cancellationToken);
            default:
                _logger.LogWarning("不支持的功能码：{FunctionCode}", functionCode);
                return CreateExceptionResponse(request, originSlaveId, 1); // 返回非法功能码异常
        }
    }

    public async Task<byte[]> MergeResponsesAsync(byte[] request, List<ModbusResponse> responseList)
    {
        // 解析请求
        var slaveId = request[6];
        var functionCode = request[7];
        var startAddress = (ushort)((request[8] << 8) | request[9]);
        var quantity = (ushort)((request[10] << 8) | request[11]);
        
        var merge = ResponseMergerFactory.GetMerger(functionCode);
        var response = merge.Merge(responseList, request);
        return response;
    }

    /// <summary>
    /// 从ModbusClient反向查找设备ID
    /// 这是一个临时解决方案，更好的做法是在调用时直接传递设备ID
    /// </summary>
    private int? GetDeviceIdFromClient(ModbusClient client)
    {
        // 遍历连接池找到匹配的客户端
        for (int deviceId = 1; deviceId <= 1000; deviceId++) // 假设设备ID范围
        {
            var rawClient = _clientPool.GetRawClient(deviceId);
            if (ReferenceEquals(rawClient, client))
            {
                return deviceId;
            }
        }
        return null;
    }

    /// <summary>
    /// 处理读取线圈请求
    /// </summary>
    private static async Task<byte[]> HandleReadCoilsAsync(ModbusClientWrapper clientWrapper, byte[] request, byte originSlaveId, CancellationToken cancellationToken)
    {
        // 解析请求
        var slaveId = request[6];
        var startAddress = (ushort)((request[8] << 8) | request[9]);
        var quantity = (ushort)((request[10] << 8) | request[11]);
        
        // 转发请求到目标设备（线程安全）
        var coils = await clientWrapper.ReadCoilsAsync(slaveId, startAddress, quantity, cancellationToken);
        
        // 构造响应
        var dataLength = (quantity + 7) / 8;
        var response = new byte[9 + dataLength];
        
        // 复制事务ID、协议ID
        Array.Copy(request, 0, response, 0, 6);
        
        // 设置响应长度
        response[4] = (byte)((dataLength + 3) >> 8);
        response[5] = (byte)(dataLength + 3);
        
        // 设置从站ID和功能码
        response[6] = originSlaveId;
        response[7] = 1; // 功能码：读取线圈
        response[8] = (byte)dataLength; // 数据长度
        
        // 复制数据
        coils.Span.CopyTo(response.AsSpan(9));
        
        return response;
    }

    /// <summary>
    /// 处理读取离散输入请求
    /// </summary>
    private static async Task<byte[]> HandleReadDiscreteInputsAsync(ModbusClientWrapper clientWrapper, byte[] request, byte originSlaveId, CancellationToken cancellationToken)
    {
        // 解析请求
        var slaveId = request[6];
        var startAddress = (ushort)((request[8] << 8) | request[9]);
        var quantity = (ushort)((request[10] << 8) | request[11]);
        
        // 转发请求到目标设备（线程安全）
        var inputs = await clientWrapper.ReadDiscreteInputsAsync(slaveId, startAddress, quantity, cancellationToken);
        
        // 构造响应
        var dataLength = (quantity + 7) / 8;
        var response = new byte[9 + dataLength];
        
        // 复制事务ID、协议ID
        Array.Copy(request, 0, response, 0, 6);
        
        // 设置响应长度
        response[4] = (byte)((dataLength + 3) >> 8);
        response[5] = (byte)(dataLength + 3);
        
        // 设置从站ID和功能码
        response[6] = originSlaveId;
        response[7] = 2; // 功能码：读取离散输入
        response[8] = (byte)dataLength; // 数据长度
        
        // 复制数据
        inputs.Span.CopyTo(response.AsSpan(9));
        
        return response;
    }

    /// <summary>
    /// 处理读取保持寄存器请求
    /// </summary>
    private static async Task<byte[]> HandleReadHoldingRegistersAsync(ModbusClientWrapper clientWrapper, byte[] request, byte originSlaveId, CancellationToken cancellationToken)
    {
        // 解析请求
        var slaveId = request[6];
        var startAddress = (ushort)((request[8] << 8) | request[9]);
        var quantity = (ushort)((request[10] << 8) | request[11]);
        
        // 转发请求到目标设备（线程安全）
        var registers = await clientWrapper.ReadHoldingRegistersAsync(slaveId, startAddress, quantity, cancellationToken);
        
        // 构造响应
        var dataLength = quantity * 2;
        var response = new byte[9 + dataLength];
        
        // 复制事务ID、协议ID
        Array.Copy(request, 0, response, 0, 6);
        
        // 设置响应长度
        response[4] = (byte)((dataLength + 3) >> 8);
        response[5] = (byte)(dataLength + 3);
        
        // 设置从站ID和功能码
        response[6] = originSlaveId;
        response[7] = 3; // 功能码：读取保持寄存器
        response[8] = (byte)dataLength; // 数据长度
        
        // 复制数据
        registers.Span.CopyTo(response.AsSpan(9));
        
        return response;
    }

    /// <summary>
    /// 处理读取输入寄存器请求
    /// </summary>
    private static async Task<byte[]> HandleReadInputRegistersAsync(ModbusClientWrapper clientWrapper, byte[] request, byte originSlaveId, CancellationToken cancellationToken)
    {
        // 解析请求
        var slaveId = request[6];
        var startAddress = (ushort)((request[8] << 8) | request[9]);
        var quantity = (ushort)((request[10] << 8) | request[11]);
        
        // 转发请求到目标设备（线程安全）
        var registers = await clientWrapper.ReadInputRegistersAsync(slaveId, startAddress, quantity, cancellationToken);
        
        // 构造响应
        var dataLength = quantity * 2;
        var response = new byte[9 + dataLength];
        
        // 复制事务ID、协议ID
        Array.Copy(request, 0, response, 0, 6);
        
        // 设置响应长度
        response[4] = (byte)((dataLength + 3) >> 8);
        response[5] = (byte)(dataLength + 3);
        
        // 设置从站ID和功能码
        response[6] = originSlaveId;
        response[7] = 4; // 功能码：读取输入寄存器
        response[8] = (byte)dataLength; // 数据长度
        
        // 复制数据
        registers.Span.CopyTo(response.AsSpan(9));
        
        return response;
    }

    /// <summary>
    /// 处理写入单个线圈请求
    /// </summary>
    private static async Task<byte[]> HandleWriteSingleCoilAsync(ModbusClientWrapper clientWrapper, byte[] request, byte originSlaveId, CancellationToken cancellationToken)
    {
        // 解析请求
        var slaveId = request[6];
        var address = (ushort)((request[8] << 8) | request[9]);
        var value = (request[10] == 0xFF);
        
        // 转发请求到目标设备（线程安全）
        await clientWrapper.WriteSingleCoilAsync(slaveId, address, value, cancellationToken);
        
        // 构造响应（与请求相同）
        var response = new byte[request.Length];
        Array.Copy(request, response, request.Length);
        response[6] = originSlaveId;
        
        return response;
    }

    /// <summary>
    /// 处理写入单个寄存器请求
    /// </summary>
    private static async Task<byte[]> HandleWriteSingleRegisterAsync(ModbusClientWrapper clientWrapper, byte[] request, byte originSlaveId, CancellationToken cancellationToken)
    {
        // 解析请求
        var slaveId = request[6];
        var address = (ushort)((request[8] << 8) | request[9]);
        var value = (ushort)((request[10] << 8) | request[11]);
        
        // 转发请求到目标设备（线程安全）
        await clientWrapper.WriteSingleRegisterAsync(slaveId, address, value, cancellationToken);
        
        // 构造响应（与请求相同）
        var response = new byte[request.Length];
        Array.Copy(request, response, request.Length);
        response[6] = originSlaveId;
        
        return response;
    }

    /// <summary>
    /// 处理写入多个线圈请求
    /// </summary>
    private static async Task<byte[]> HandleWriteMultipleCoilsAsync(ModbusClientWrapper clientWrapper, byte[] request, byte originSlaveId, CancellationToken cancellationToken)
    {
        // 解析请求
        var slaveId = request[6];
        var startAddress = (ushort)((request[8] << 8) | request[9]);
        var quantity = (ushort)((request[10] << 8) | request[11]);
        var byteCount = request[12];
        
        // 提取线圈值
        var values = new bool[quantity];
        for (var i = 0; i < quantity; i++)
        {
            var byteIndex = 13 + (i / 8);
            var bitIndex = i % 8;
            values[i] = ((request[byteIndex] >> bitIndex) & 1) == 1;
        }
        
        // 转发请求到目标设备（线程安全）
        await clientWrapper.WriteMultipleCoilsAsync(slaveId, startAddress, values, cancellationToken);
        
        // 构造响应
        var response = new byte[12];
        
        // 复制事务ID、协议ID
        Array.Copy(request, 0, response, 0, 6);
        
        // 设置响应长度
        response[4] = 0;
        response[5] = 6;
        
        // 设置从站ID和功能码
        response[6] = originSlaveId;
        response[7] = 15; // 功能码：写入多个线圈
        
        // 设置起始地址
        response[8] = (byte)(startAddress >> 8);
        response[9] = (byte)startAddress;
        
        // 设置数量
        response[10] = (byte)(quantity >> 8);
        response[11] = (byte)quantity;
        
        return response;
    }

    /// <summary>
    /// 处理写入多个寄存器请求
    /// </summary>
    private static async Task<byte[]> HandleWriteMultipleRegistersAsync(ModbusClientWrapper clientWrapper, byte[] request, byte originSlaveId, CancellationToken cancellationToken)
    {
        // 解析请求
        var slaveId = request[6];
        var startAddress = (ushort)((request[8] << 8) | request[9]);
        var quantity = (ushort)((request[10] << 8) | request[11]);
        var byteCount = request[12];
        
        // 提取寄存器值
        var values = new ushort[quantity];
        for (var i = 0; i < quantity; i++)
        {
            values[i] = (ushort)((request[13 + i * 2] << 8) | request[14 + i * 2]);
        }
        
        // 转发请求到目标设备（线程安全）
        await clientWrapper.WriteMultipleRegistersAsync(slaveId, startAddress, values, cancellationToken);
        
        // 构造响应
        var response = new byte[12];
        
        // 复制事务ID、协议ID
        Array.Copy(request, 0, response, 0, 6);
        
        // 设置响应长度
        response[4] = 0;
        response[5] = 6;
        
        // 设置从站ID和功能码
        response[6] = originSlaveId;
        response[7] = 16; // 功能码：写入多个寄存器
        
        // 设置起始地址
        response[8] = (byte)(startAddress >> 8);
        response[9] = (byte)startAddress;
        
        // 设置数量
        response[10] = (byte)(quantity >> 8);
        response[11] = (byte)quantity;
        
        return response;
    }

    /// <summary>
    /// 创建异常响应
    /// </summary>
    private static byte[] CreateExceptionResponse(byte[] request, byte slaveId, byte exceptionCode)
    {
        var response = new byte[9];
        
        // 复制事务ID、协议ID
        Array.Copy(request, 0, response, 0, 6);
        
        // 设置响应长度
        response[4] = 0;
        response[5] = 3;
        
        // 设置从站ID和异常功能码
        response[6] = slaveId;
        response[7] = (byte)(request[7] | 0x80); // 功能码 + 0x80 表示异常
        response[8] = exceptionCode;
        
        return response;
    }
}
