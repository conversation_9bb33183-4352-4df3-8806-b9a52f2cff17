using System.Collections.Concurrent;
using System.Threading.Channels;
using Gsdt.ModbusGateway.Models;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace Gsdt.ModbusGateway.Services;

/// <summary>
/// Modbus请求调度器
/// 使用消息队列确保对每个设备的请求串行处理
/// </summary>
public class ModbusRequestScheduler : BackgroundService, IDisposable
{
    private readonly ILogger<ModbusRequestScheduler> _logger;
    private readonly ConcurrentDictionary<int, DeviceRequestQueue> _deviceQueues = new();
    private readonly ConcurrentDictionary<int, ModbusClient> _clients = new();
    private bool _disposed = false;

    public ModbusRequestScheduler(ILogger<ModbusRequestScheduler> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 注册设备客户端
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="client">ModbusClient</param>
    public void RegisterDevice(int deviceId, ModbusClient client)
    {
        _clients[deviceId] = client;
        _deviceQueues[deviceId] = new DeviceRequestQueue(deviceId, client, _logger);
    }

    /// <summary>
    /// 提交请求到调度器
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="request">请求数据</param>
    /// <param name="originSlaveId">原始从站ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>响应数据</returns>
    public async Task<byte[]> SubmitRequestAsync(int deviceId, byte[] request, byte originSlaveId, CancellationToken cancellationToken)
    {
        if (!_deviceQueues.TryGetValue(deviceId, out var queue))
        {
            throw new InvalidOperationException($"设备 {deviceId} 未注册");
        }

        var requestItem = new ModbusRequestItem
        {
            Request = request,
            OriginSlaveId = originSlaveId,
            CompletionSource = new TaskCompletionSource<byte[]>(),
            CancellationToken = cancellationToken
        };

        await queue.EnqueueAsync(requestItem, cancellationToken);
        return await requestItem.CompletionSource.Task;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Modbus请求调度器已启动");

        // 启动所有设备队列的处理任务
        var tasks = _deviceQueues.Values.Select(queue => queue.ProcessRequestsAsync(stoppingToken)).ToArray();

        try
        {
            await Task.WhenAll(tasks);
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("Modbus请求调度器正在停止");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Modbus请求调度器发生错误");
        }
    }

    public override void Dispose()
    {
        if (_disposed) return;

        foreach (var queue in _deviceQueues.Values)
        {
            queue.Dispose();
        }
        
        _deviceQueues.Clear();
        _clients.Clear();
        _disposed = true;
        
        base.Dispose();
    }
}

/// <summary>
/// 设备请求队列
/// 为每个设备维护一个独立的请求队列，确保串行处理
/// </summary>
public class DeviceRequestQueue : IDisposable
{
    private readonly int _deviceId;
    private readonly ModbusClient _client;
    private readonly ILogger _logger;
    private readonly Channel<ModbusRequestItem> _channel;
    private readonly ChannelWriter<ModbusRequestItem> _writer;
    private readonly ChannelReader<ModbusRequestItem> _reader;
    private bool _disposed = false;

    public DeviceRequestQueue(int deviceId, ModbusClient client, ILogger logger)
    {
        _deviceId = deviceId;
        _client = client;
        _logger = logger;
        
        // 创建无界通道用于请求队列
        var options = new UnboundedChannelOptions
        {
            SingleReader = true,
            SingleWriter = false,
            AllowSynchronousContinuations = false
        };
        
        _channel = Channel.CreateUnbounded<ModbusRequestItem>(options);
        _writer = _channel.Writer;
        _reader = _channel.Reader;
    }

    /// <summary>
    /// 将请求加入队列
    /// </summary>
    /// <param name="requestItem">请求项</param>
    /// <param name="cancellationToken">取消令牌</param>
    public async Task EnqueueAsync(ModbusRequestItem requestItem, CancellationToken cancellationToken)
    {
        if (_disposed)
        {
            throw new ObjectDisposedException(nameof(DeviceRequestQueue));
        }

        await _writer.WriteAsync(requestItem, cancellationToken);
    }

    /// <summary>
    /// 处理请求队列（串行处理）
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    public async Task ProcessRequestsAsync(CancellationToken cancellationToken)
    {
        _logger.LogDebug("设备 {DeviceId} 的请求处理队列已启动", _deviceId);

        try
        {
            await foreach (var requestItem in _reader.ReadAllAsync(cancellationToken))
            {
                try
                {
                    // 串行处理每个请求
                    var response = await ProcessSingleRequestAsync(requestItem, cancellationToken);
                    requestItem.CompletionSource.SetResult(response);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "处理设备 {DeviceId} 的请求时发生错误", _deviceId);
                    requestItem.CompletionSource.SetException(ex);
                }
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogDebug("设备 {DeviceId} 的请求处理队列已停止", _deviceId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "设备 {DeviceId} 的请求处理队列发生错误", _deviceId);
        }
    }

    /// <summary>
    /// 处理单个请求
    /// </summary>
    private async Task<byte[]> ProcessSingleRequestAsync(ModbusRequestItem requestItem, CancellationToken cancellationToken)
    {
        var request = requestItem.Request;
        var originSlaveId = requestItem.OriginSlaveId;
        
        // 获取从站ID和功能码
        var slaveId = request[6];
        var functionCode = request[7];
        
        _logger.LogDebug("处理设备 {DeviceId} 的请求: FunctionCode={FunctionCode}, SlaveId={SlaveId}", 
            _deviceId, functionCode, slaveId);
        
        // 根据功能码处理请求
        switch (functionCode)
        {
            case 1: // 读取线圈
                return await HandleReadCoilsAsync(_client, request, originSlaveId, cancellationToken);
            case 2: // 读取离散输入
                return await HandleReadDiscreteInputsAsync(_client, request, originSlaveId, cancellationToken);
            case 3: // 读取保持寄存器
                return await HandleReadHoldingRegistersAsync(_client, request, originSlaveId, cancellationToken);
            case 4: // 读取输入寄存器
                return await HandleReadInputRegistersAsync(_client, request, originSlaveId, cancellationToken);
            case 5: // 写入单个线圈
                return await HandleWriteSingleCoilAsync(_client, request, originSlaveId, cancellationToken);
            case 6: // 写入单个寄存器
                return await HandleWriteSingleRegisterAsync(_client, request, originSlaveId, cancellationToken);
            case 15: // 写入多个线圈
                return await HandleWriteMultipleCoilsAsync(_client, request, originSlaveId, cancellationToken);
            case 16: // 写入多个寄存器
                return await HandleWriteMultipleRegistersAsync(_client, request, originSlaveId, cancellationToken);
            default:
                _logger.LogWarning("不支持的功能码：{FunctionCode}", functionCode);
                return CreateExceptionResponse(request, originSlaveId, 1); // 返回非法功能码异常
        }
    }

    // 这里可以复用原有的处理方法，或者调用原有的ModbusRequestHandler
    private static async Task<byte[]> HandleReadCoilsAsync(ModbusClient client, byte[] request, byte originSlaveId, CancellationToken cancellationToken)
    {
        // 解析请求
        var slaveId = request[6];
        var startAddress = (ushort)((request[8] << 8) | request[9]);
        var quantity = (ushort)((request[10] << 8) | request[11]);
        
        // 转发请求到目标设备
        var coils = await client.ReadCoilsAsync(slaveId, startAddress, quantity, cancellationToken);
        
        // 构造响应
        var dataLength = (quantity + 7) / 8;
        var response = new byte[9 + dataLength];
        
        // 复制事务ID、协议ID
        Array.Copy(request, 0, response, 0, 6);
        
        // 设置响应长度
        response[4] = (byte)((dataLength + 3) >> 8);
        response[5] = (byte)(dataLength + 3);
        
        // 设置从站ID和功能码
        response[6] = originSlaveId;
        response[7] = 1; // 功能码：读取线圈
        response[8] = (byte)dataLength; // 数据长度
        
        // 复制数据
        coils.Span.CopyTo(response.AsSpan(9));
        
        return response;
    }

    // 其他处理方法的实现类似，这里省略以节省空间
    private static async Task<byte[]> HandleReadDiscreteInputsAsync(ModbusClient client, byte[] request, byte originSlaveId, CancellationToken cancellationToken)
    {
        // 实现类似HandleReadCoilsAsync
        throw new NotImplementedException("需要实现其他功能码的处理方法");
    }

    private static async Task<byte[]> HandleReadHoldingRegistersAsync(ModbusClient client, byte[] request, byte originSlaveId, CancellationToken cancellationToken)
    {
        // 实现类似HandleReadCoilsAsync
        throw new NotImplementedException("需要实现其他功能码的处理方法");
    }

    private static async Task<byte[]> HandleReadInputRegistersAsync(ModbusClient client, byte[] request, byte originSlaveId, CancellationToken cancellationToken)
    {
        // 实现类似HandleReadCoilsAsync
        throw new NotImplementedException("需要实现其他功能码的处理方法");
    }

    private static async Task<byte[]> HandleWriteSingleCoilAsync(ModbusClient client, byte[] request, byte originSlaveId, CancellationToken cancellationToken)
    {
        // 实现类似HandleReadCoilsAsync
        throw new NotImplementedException("需要实现其他功能码的处理方法");
    }

    private static async Task<byte[]> HandleWriteSingleRegisterAsync(ModbusClient client, byte[] request, byte originSlaveId, CancellationToken cancellationToken)
    {
        // 实现类似HandleReadCoilsAsync
        throw new NotImplementedException("需要实现其他功能码的处理方法");
    }

    private static async Task<byte[]> HandleWriteMultipleCoilsAsync(ModbusClient client, byte[] request, byte originSlaveId, CancellationToken cancellationToken)
    {
        // 实现类似HandleReadCoilsAsync
        throw new NotImplementedException("需要实现其他功能码的处理方法");
    }

    private static async Task<byte[]> HandleWriteMultipleRegistersAsync(ModbusClient client, byte[] request, byte originSlaveId, CancellationToken cancellationToken)
    {
        // 实现类似HandleReadCoilsAsync
        throw new NotImplementedException("需要实现其他功能码的处理方法");
    }

    private static byte[] CreateExceptionResponse(byte[] request, byte slaveId, byte exceptionCode)
    {
        var response = new byte[9];
        
        // 复制事务ID、协议ID
        Array.Copy(request, 0, response, 0, 6);
        
        // 设置响应长度
        response[4] = 0;
        response[5] = 3;
        
        // 设置从站ID和异常功能码
        response[6] = slaveId;
        response[7] = (byte)(request[7] | 0x80); // 功能码 + 0x80 表示异常
        response[8] = exceptionCode;
        
        return response;
    }

    public void Dispose()
    {
        if (_disposed) return;
        
        _writer.Complete();
        _disposed = true;
    }
}

/// <summary>
/// Modbus请求项
/// </summary>
public class ModbusRequestItem
{
    public byte[] Request { get; set; } = Array.Empty<byte>();
    public byte OriginSlaveId { get; set; }
    public TaskCompletionSource<byte[]> CompletionSource { get; set; } = new();
    public CancellationToken CancellationToken { get; set; }
}
