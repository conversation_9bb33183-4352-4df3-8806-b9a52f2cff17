using System.Net;
using Gsdt.ModbusGateway.Services;
using Microsoft.Extensions.Logging;
using NetCoreServer;

namespace Gsdt.ModbusGateway.Models;

/// <summary>
/// 线程安全的Modbus TCP Session
/// 使用ThreadSafeModbusClientPool确保并发安全
/// </summary>
public class ThreadSafeModbusTcpSession : TcpSession
{
    private readonly ILogger<ModbusTcpGatewayService> _logger;
    private readonly IModbusRequestHandler _modbusRequestHandler;
    private readonly ThreadSafeModbusClientPool _clientPool;
    private readonly GatewayConfig _config;
    private readonly CancellationTokenSource _stoppingCts;

    public ThreadSafeModbusTcpSession(
        TcpServer server,
        ILogger<ModbusTcpGatewayService> logger,
        IModbusRequestHandler modbusRequestHandler,
        ThreadSafeModbusClientPool clientPool,
        GatewayConfig config,
        CancellationTokenSource stoppingCts) : base(server)
    {
        _logger = logger;
        _modbusRequestHandler = modbusRequestHandler;
        _clientPool = clientPool;
        _config = config;
        _stoppingCts = stoppingCts;
    }

    protected override void OnReceived(byte[] buffer, long offset, long size)
    {
        // 创建请求的副本
        var request = new byte[size];
        Array.Copy(buffer, request, size);
        
        // 异步处理请求但不等待
        _ = ProcessRequestAsync(request).ContinueWith(t => 
        {
            if (t.IsFaulted)
            {
                _logger.LogError(t.Exception, $"处理来自 {Server.Address}:{Server.Port} 的请求时发生未处理的异常");
            }
        }, TaskScheduler.Current);
    }

    private async Task ProcessRequestAsync(byte[] request)
    {
        try
        {
            // 获取从站ID（第7个字节，因为Modbus TCP有6字节的MBAP头）
            var slaveId = request[6];

            // 获取功能码（第8个字节）
            var functionCode = request[7];

            // 获取起始地址（第9和10个字节）
            var startAddress = (ushort)((request[8] << 8) | request[9]);

            // 获取数量（第11和12个字节）
            var quantity = (ushort)((request[10] << 8) | request[11]);

            _logger.LogInformation(
                "收到请求：Port={Port}, FunctionCode={FunctionCode}, StartAddress={startAddress}, Quantity={quantity}",
                Server.Port, functionCode, startAddress, quantity);

            // 转换请求
            var requests = await _modbusRequestHandler.TransformRequestAsync(
                new ModbusRequest
                {
                    SlaveId = slaveId,
                    FunctionCode = functionCode,
                    StartAddress = startAddress,
                    Quantity = quantity
                }, _config.RoutingRules);

            var responseList = new List<ModbusResponse>();
            foreach (var modbusRequest in requests)
            {
                _logger.LogInformation(
                    $"转发请求到: DeviceId={modbusRequest.DeviceId}, FunctionCode={functionCode}, SlaveId={modbusRequest.SlaveId}, StartAddress={modbusRequest.StartAddress}, Quantity={modbusRequest.Quantity}");
                
                // 获取线程安全的客户端包装器
                var clientWrapper = GetThreadSafeModbusClient(modbusRequest);
                
                // 修改请求报文中的从机地址、起始地址和寄存器数量
                var newRequest = ModifyRequest(request, modbusRequest.SlaveId, modbusRequest.StartAddress,
                    modbusRequest.Quantity);
                
                // 使用线程安全的请求处理器
                var data = await ProcessThreadSafeRequestAsync(clientWrapper, newRequest, slaveId, modbusRequest.FunctionCode);
                
                responseList.Add(new ModbusResponse
                {
                    Data = data,
                    SourceStartAddress = modbusRequest.SourceStartAddress
                });
            }

            // 合并响应报文
            var response = await _modbusRequestHandler.MergeResponsesAsync(request, responseList);
            SendAsync(response);
        }
        catch (InvalidModbusRequestException ex)
        {
            var slaveId = request[6];
            SendExceptionResponse(request, slaveId, ex.ExceptionCode);
        }
        catch (SlaveException ex)
        {
            var slaveId = request[6];
            _logger.LogError(ex, "目标设备返回错误: {Message},功能码={FunctionCode}, 错误码={ExceptionCode}", ex.Message,
                ex.FunctionCode, ex.SlaveExceptionCode);
            SendExceptionResponse(request, slaveId, (byte)ModbusExceptionCode.GatewayTargetDeviceFailedToRespond);
        }
        catch (Exception e)
        {
            var slaveId = request[6];
            _logger.LogError(e, "处理Modbus请求时发生错误");
            SendExceptionResponse(request, slaveId, (byte)ModbusExceptionCode.GatewayTargetDeviceFailedToRespond);
        }
    }

    /// <summary>
    /// 获取线程安全的ModbusClient包装器
    /// </summary>
    private ModbusClientWrapper GetThreadSafeModbusClient(ModbusRequest modbusRequest)
    {
        // 查找对应的目标设备
        var targetDevice = _config.TargetDevices.FirstOrDefault(d => d.Id == modbusRequest.DeviceId);
        if (targetDevice == null)
        {
            _logger.LogWarning("未找到ID为 {DeviceId} 的目标设备", modbusRequest.DeviceId);
            throw new InvalidModbusRequestException($"未找到ID为 {modbusRequest.DeviceId} 的目标设备",
                (byte)ModbusExceptionCode.GatewayPathUnavailable);
        }

        // 获取线程安全的客户端包装器
        var clientWrapper = _clientPool.GetClientWrapper(targetDevice.Id);
        if (clientWrapper == null)
        {
            _logger.LogWarning("未找到ID为 {DeviceId} 的Modbus客户端包装器", targetDevice.Id);
            throw new InvalidModbusRequestException($"未找到ID为 {targetDevice.Id} 的Modbus客户端包装器",
                (byte)ModbusExceptionCode.GatewayPathUnavailable);
        }

        // 检查连接状态（使用原始客户端）
        var rawClient = _clientPool.GetRawClient(targetDevice.Id);
        if (rawClient != null)
        {
            EnsureClientConnected(targetDevice, rawClient);
        }

        return clientWrapper;
    }

    /// <summary>
    /// 确保客户端已连接
    /// </summary>
    private void EnsureClientConnected(TargetDevice targetDevice, ModbusClient rawClient)
    {
        if (targetDevice.PortType == PortType.Tcp)
        {
            if (targetDevice is TcpTargetDevice tcpTargetDevice && 
                rawClient is ModbusTcpClient targetTcpClient)
            {
                if (!targetTcpClient.IsConnected)
                {
                    _logger.LogInformation("重新连接到设备 {DeviceId} ({IpAddress}:{Port})",
                        tcpTargetDevice.Id, tcpTargetDevice.Ip, tcpTargetDevice.Port);

                    try
                    {
                        targetTcpClient.Connect(new IPEndPoint(IPAddress.Parse(tcpTargetDevice.Ip),
                            tcpTargetDevice.Port));
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "重新连接到设备 {DeviceId} 失败", tcpTargetDevice.Id);
                        throw new InvalidModbusRequestException($"重新连接到设备 {tcpTargetDevice.Id} 失败",
                            (byte)ModbusExceptionCode.GatewayTargetDeviceFailedToRespond);
                    }
                }
            }
        }
        else if (targetDevice.PortType == PortType.Rtu)
        {
            if (targetDevice is RtuTargetDevice rtuTargetDevice && 
                rawClient is ModbusRtuClient targetRtuClient)
            {
                if (!targetRtuClient.IsConnected)
                {
                    _logger.LogInformation("重新连接到设备 {DeviceId}, PortName = {Port})", rtuTargetDevice.Id,
                        rtuTargetDevice.PortName);

                    try
                    {
                        targetRtuClient.Connect(new SerialPort(rtuTargetDevice.PortName, rtuTargetDevice.BaudRate,
                            rtuTargetDevice.Parity, rtuTargetDevice.DataBits, rtuTargetDevice.StopBits));
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "重新连接到设备 {DeviceId} 失败", rtuTargetDevice.Id);
                        throw new InvalidModbusRequestException($"重新连接到设备 {rtuTargetDevice.Id} 失败",
                            (byte)ModbusExceptionCode.GatewayTargetDeviceFailedToRespond);
                    }
                }
            }
        }
    }

    /// <summary>
    /// 使用线程安全的方式处理请求
    /// </summary>
    private async Task<byte[]> ProcessThreadSafeRequestAsync(ModbusClientWrapper clientWrapper, byte[] request, byte originSlaveId, byte functionCode)
    {
        // 解析请求参数
        var slaveId = request[6];
        var startAddress = (ushort)((request[8] << 8) | request[9]);
        var quantity = (ushort)((request[10] << 8) | request[11]);

        // 根据功能码调用相应的线程安全方法
        switch (functionCode)
        {
            case 1: // 读取线圈
                var coils = await clientWrapper.ReadCoilsAsync(slaveId, startAddress, quantity, _stoppingCts.Token);
                return BuildCoilsResponse(request, originSlaveId, coils, quantity);
                
            case 2: // 读取离散输入
                var inputs = await clientWrapper.ReadDiscreteInputsAsync(slaveId, startAddress, quantity, _stoppingCts.Token);
                return BuildDiscreteInputsResponse(request, originSlaveId, inputs, quantity);
                
            case 3: // 读取保持寄存器
                var holdingRegisters = await clientWrapper.ReadHoldingRegistersAsync(slaveId, startAddress, quantity, _stoppingCts.Token);
                return BuildHoldingRegistersResponse(request, originSlaveId, holdingRegisters, quantity);
                
            case 4: // 读取输入寄存器
                var inputRegisters = await clientWrapper.ReadInputRegistersAsync(slaveId, startAddress, quantity, _stoppingCts.Token);
                return BuildInputRegistersResponse(request, originSlaveId, inputRegisters, quantity);
                
            case 5: // 写入单个线圈
                var coilValue = (request[10] == 0xFF);
                await clientWrapper.WriteSingleCoilAsync(slaveId, startAddress, coilValue, _stoppingCts.Token);
                return BuildWriteSingleCoilResponse(request, originSlaveId);
                
            case 6: // 写入单个寄存器
                var registerValue = (ushort)((request[10] << 8) | request[11]);
                await clientWrapper.WriteSingleRegisterAsync(slaveId, startAddress, registerValue, _stoppingCts.Token);
                return BuildWriteSingleRegisterResponse(request, originSlaveId);
                
            case 15: // 写入多个线圈
                var coilValues = ExtractCoilValues(request, quantity);
                await clientWrapper.WriteMultipleCoilsAsync(slaveId, startAddress, coilValues, _stoppingCts.Token);
                return BuildWriteMultipleCoilsResponse(request, originSlaveId, startAddress, quantity);
                
            case 16: // 写入多个寄存器
                var registerValues = ExtractRegisterValues(request, quantity);
                await clientWrapper.WriteMultipleRegistersAsync(slaveId, startAddress, registerValues, _stoppingCts.Token);
                return BuildWriteMultipleRegistersResponse(request, originSlaveId, startAddress, quantity);
                
            default:
                throw new InvalidModbusRequestException($"不支持的功能码: {functionCode}", 
                    (byte)ModbusExceptionCode.IllegalFunction);
        }
    }

    // 响应构建方法
    private static byte[] BuildCoilsResponse(byte[] request, byte originSlaveId, Memory<byte> coils, ushort quantity)
    {
        var dataLength = (quantity + 7) / 8;
        var response = new byte[9 + dataLength];
        
        Array.Copy(request, 0, response, 0, 6);
        response[4] = (byte)((dataLength + 3) >> 8);
        response[5] = (byte)(dataLength + 3);
        response[6] = originSlaveId;
        response[7] = 1;
        response[8] = (byte)dataLength;
        
        coils.Span.CopyTo(response.AsSpan(9));
        return response;
    }

    private static byte[] BuildDiscreteInputsResponse(byte[] request, byte originSlaveId, Memory<byte> inputs, ushort quantity)
    {
        var dataLength = (quantity + 7) / 8;
        var response = new byte[9 + dataLength];
        
        Array.Copy(request, 0, response, 0, 6);
        response[4] = (byte)((dataLength + 3) >> 8);
        response[5] = (byte)(dataLength + 3);
        response[6] = originSlaveId;
        response[7] = 2;
        response[8] = (byte)dataLength;
        
        inputs.Span.CopyTo(response.AsSpan(9));
        return response;
    }

    private static byte[] BuildHoldingRegistersResponse(byte[] request, byte originSlaveId, Memory<byte> registers, ushort quantity)
    {
        var dataLength = quantity * 2;
        var response = new byte[9 + dataLength];
        
        Array.Copy(request, 0, response, 0, 6);
        response[4] = (byte)((dataLength + 3) >> 8);
        response[5] = (byte)(dataLength + 3);
        response[6] = originSlaveId;
        response[7] = 3;
        response[8] = (byte)dataLength;
        
        registers.Span.CopyTo(response.AsSpan(9));
        return response;
    }

    private static byte[] BuildInputRegistersResponse(byte[] request, byte originSlaveId, Memory<byte> registers, ushort quantity)
    {
        var dataLength = quantity * 2;
        var response = new byte[9 + dataLength];
        
        Array.Copy(request, 0, response, 0, 6);
        response[4] = (byte)((dataLength + 3) >> 8);
        response[5] = (byte)(dataLength + 3);
        response[6] = originSlaveId;
        response[7] = 4;
        response[8] = (byte)dataLength;
        
        registers.Span.CopyTo(response.AsSpan(9));
        return response;
    }

    private static byte[] BuildWriteSingleCoilResponse(byte[] request, byte originSlaveId)
    {
        var response = new byte[request.Length];
        Array.Copy(request, response, request.Length);
        response[6] = originSlaveId;
        return response;
    }

    private static byte[] BuildWriteSingleRegisterResponse(byte[] request, byte originSlaveId)
    {
        var response = new byte[request.Length];
        Array.Copy(request, response, request.Length);
        response[6] = originSlaveId;
        return response;
    }

    private static byte[] BuildWriteMultipleCoilsResponse(byte[] request, byte originSlaveId, ushort startAddress, ushort quantity)
    {
        var response = new byte[12];
        Array.Copy(request, 0, response, 0, 6);
        response[4] = 0;
        response[5] = 6;
        response[6] = originSlaveId;
        response[7] = 15;
        response[8] = (byte)(startAddress >> 8);
        response[9] = (byte)startAddress;
        response[10] = (byte)(quantity >> 8);
        response[11] = (byte)quantity;
        return response;
    }

    private static byte[] BuildWriteMultipleRegistersResponse(byte[] request, byte originSlaveId, ushort startAddress, ushort quantity)
    {
        var response = new byte[12];
        Array.Copy(request, 0, response, 0, 6);
        response[4] = 0;
        response[5] = 6;
        response[6] = originSlaveId;
        response[7] = 16;
        response[8] = (byte)(startAddress >> 8);
        response[9] = (byte)startAddress;
        response[10] = (byte)(quantity >> 8);
        response[11] = (byte)quantity;
        return response;
    }

    // 数据提取方法
    private static bool[] ExtractCoilValues(byte[] request, ushort quantity)
    {
        var values = new bool[quantity];
        for (var i = 0; i < quantity; i++)
        {
            var byteIndex = 13 + (i / 8);
            var bitIndex = i % 8;
            values[i] = ((request[byteIndex] >> bitIndex) & 1) == 1;
        }
        return values;
    }

    private static ushort[] ExtractRegisterValues(byte[] request, ushort quantity)
    {
        var values = new ushort[quantity];
        for (var i = 0; i < quantity; i++)
        {
            values[i] = (ushort)((request[13 + i * 2] << 8) | request[14 + i * 2]);
        }
        return values;
    }

    // 其他辅助方法（ModifyRequest, SendExceptionResponse等）可以从原始实现中复制
    private byte[] ModifyRequest(byte[] request, byte newSlaveId, ushort newStartAddress, ushort newQuantity)
    {
        // 实现与原始ModifyRequest相同的逻辑
        var modifiedRequest = new byte[request.Length];
        Array.Copy(request, modifiedRequest, request.Length);

        const int slaveIdOffset = 6;
        const int functionCodeOffset = 7;
        const int startAddressOffset = 8;
        const int quantityOffset = 10;

        if (modifiedRequest.Length < 12)
        {
            throw new ArgumentException("Invalid Modbus TCP request length");
        }

        modifiedRequest[slaveIdOffset] = newSlaveId;
        modifiedRequest[startAddressOffset] = (byte)(newStartAddress >> 8);
        modifiedRequest[startAddressOffset + 1] = (byte)(newStartAddress & 0xFF);

        if (modifiedRequest[functionCodeOffset] < 5)
        {
            modifiedRequest[quantityOffset] = (byte)(newQuantity >> 8);
            modifiedRequest[quantityOffset + 1] = (byte)(newQuantity & 0xFF);
        }

        return modifiedRequest;
    }

    private void SendExceptionResponse(byte[] request, byte slaveId, byte exceptionCode)
    {
        var response = new byte[9];
        Array.Copy(request, 0, response, 0, 6);
        response[4] = 0;
        response[5] = 3;
        response[6] = slaveId;
        response[7] = (byte)(request[7] | 0x80);
        response[8] = exceptionCode;
        SendAsync(response);
    }
}
